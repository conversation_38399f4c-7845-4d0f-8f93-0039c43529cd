F:\pythonproject\AIphone\ai_phone_agent_server\yudao-module-aiphoneagent\yudao-module-monitor\src\main\java\cn\iocoder\yudao\module\monitor\config\DeviceMonitorProperties.java
F:\pythonproject\AIphone\ai_phone_agent_server\yudao-module-aiphoneagent\yudao-module-monitor\src\main\java\cn\iocoder\yudao\module\monitor\controller\DeviceController.java
F:\pythonproject\AIphone\ai_phone_agent_server\yudao-module-aiphoneagent\yudao-module-monitor\src\main\java\cn\iocoder\yudao\module\monitor\controller\DeviceReportController.java
F:\pythonproject\AIphone\ai_phone_agent_server\yudao-module-aiphoneagent\yudao-module-monitor\src\main\java\cn\iocoder\yudao\module\monitor\dto\DeviceReportReqDTO.java
F:\pythonproject\AIphone\ai_phone_agent_server\yudao-module-aiphoneagent\yudao-module-monitor\src\main\java\cn\iocoder\yudao\module\monitor\dto\DeviceStatusCommandDTO.java
F:\pythonproject\AIphone\ai_phone_agent_server\yudao-module-aiphoneagent\yudao-module-monitor\src\main\java\cn\iocoder\yudao\module\monitor\dto\DeviceStatusRespDTO.java
F:\pythonproject\AIphone\ai_phone_agent_server\yudao-module-aiphoneagent\yudao-module-monitor\src\main\java\cn\iocoder\yudao\module\monitor\job\DeviceOfflineCheckJob.java
F:\pythonproject\AIphone\ai_phone_agent_server\yudao-module-aiphoneagent\yudao-module-monitor\src\main\java\cn\iocoder\yudao\module\monitor\service\DeviceService.java
F:\pythonproject\AIphone\ai_phone_agent_server\yudao-module-aiphoneagent\yudao-module-monitor\src\main\java\cn\iocoder\yudao\module\monitor\service\impl\DeviceServiceImpl.java
