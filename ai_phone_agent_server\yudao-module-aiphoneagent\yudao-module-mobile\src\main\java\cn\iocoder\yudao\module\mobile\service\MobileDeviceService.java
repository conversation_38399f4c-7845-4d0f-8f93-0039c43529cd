package cn.iocoder.yudao.module.mobile.service;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mobile.controller.admin.vo.MobileDevicePageReqVO;
import cn.iocoder.yudao.module.mobile.controller.admin.vo.MobileDeviceSaveReqVO;
import cn.iocoder.yudao.module.mobile.dal.dataobject.MobileDeviceDO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 手机设备管理 Service 接口
 *
 * <AUTHOR>
 */
public interface MobileDeviceService {

    /**
     * 更新手机设备
     *
     * @param updateReqVO 更新信息
     */
    void updateMobileDevice(@Valid MobileDeviceSaveReqVO updateReqVO);

    // 🔥 重构：移除单独删除手机设备的功能
    // 手机设备只能通过删除代理器来级联删除

    /**
     * 根据代理器ID删除关联的手机设备（级联删除）
     *
     * @param machineId 代理器ID
     * @return 删除的记录数
     */
    int deleteMobileDevicesByMachineId(String machineId);

    /**
     * 获得手机设备
     *
     * @param id 编号
     * @return 手机设备
     */
    MobileDeviceDO getMobileDevice(Long id);

    /**
     * 获得手机设备分页
     *
     * @param pageReqVO 分页查询
     * @return 手机设备分页
     */
    PageResult<MobileDeviceDO> getMobileDevicePage(MobileDevicePageReqVO pageReqVO);

    /**
     * 获得手机设备列表
     *
     * @return 手机设备列表
     */
    List<MobileDeviceDO> getMobileDeviceList();

    /**
     * 暂停手机设备
     *
     * @param id 手机设备ID
     */
    void pauseMobileDevice(Long id);

    /**
     * 启用手机设备
     *
     * @param id 手机设备ID
     */
    void enableMobileDevice(Long id);

    /**
     * 根据代理器ID获取关联的手机设备列表
     *
     * @param machineId 代理器ID
     * @return 手机设备列表
     */
    List<MobileDeviceDO> getMobileDevicesByMachineId(String machineId);

    /**
     * 更新手机设备的最后上报时间
     *
     * @param deviceId 设备ID
     */
    void updateLastReportTime(String deviceId);

    /**
     * 🔥 新增：更新手机设备状态
     * 用于离线检测定时任务同步状态
     *
     * @param id 设备主键ID
     * @param status 新状态（0-启用，1-暂停，2-离线）
     */
    void updateDeviceStatus(Long id, Integer status);

    /**
     * 🔥 新增：根据设备ID更新状态
     * 用于离线检测定时任务同步状态
     *
     * @param deviceId 设备ID
     * @param status 新状态（0-启用，1-暂停，2-离线）
     */
    void updateDeviceStatusByDeviceId(String deviceId, Integer status);

    /**
     * 🔥 新增：获取所有活跃状态的手机设备
     * 用于离线检测定时任务
     *
     * @return 活跃状态的手机设备列表（排除已删除的）
     */
    List<MobileDeviceDO> getActiveDevices();

    /**
     * 更新手机设备的应用状态
     *
     * @param deviceId 设备ID
     * @param douyinStatus 抖音状态
     * @param xiaohongshuStatus 小红书状态
     * @param videoStatus 视频号状态
     * @param officialAccountStatus 公众号状态
     * @param qywxStatus 企微状态
     * @param wechatStatus 微信状态
     * @param dingtalkStatus 钉钉状态
     * @param feishuStatus 飞书状态
     */
    void updateAppStatuses(String deviceId, Integer douyinStatus, Integer xiaohongshuStatus,
                          Integer videoStatus, Integer officialAccountStatus, Integer qywxStatus,
                          Integer wechatStatus, Integer dingtalkStatus, Integer feishuStatus);

    /**
     * 根据设备ID获取手机设备
     *
     * @param deviceId 设备ID
     * @return 手机设备
     */
    MobileDeviceDO getMobileDeviceByDeviceId(String deviceId);

    /**
     * 🔥 新增：根据代理器ID更新关联手机设备的租户ID
     * 用于代理器租户迁移时同步更新手机设备
     *
     * @param machineId 代理器ID
     * @param newTenantId 新的租户ID
     * @return 更新的设备数量
     */
    int updateTenantIdByMachineId(String machineId, Long newTenantId);

    /**
     * 🔥 新增：自动创建手机设备（用于设备自动发现）
     *
     * @param deviceId 设备ID
     * @param machineId 机器ID
     * @param douyinStatus 抖音状态
     * @param xiaohongshuStatus 小红书状态
     * @param videoStatus 视频号状态
     * @param officialAccountStatus 公众号状态
     * @param qywxStatus 企微状态
     * @param wechatStatus 微信状态
     * @param dingtalkStatus 钉钉状态
     * @param feishuStatus 飞书状态
     * @return 创建的手机设备
     */
    MobileDeviceDO createMobileDeviceFromReport(String deviceId, String machineId,
                                               Integer douyinStatus, Integer xiaohongshuStatus,
                                               Integer videoStatus, Integer officialAccountStatus,
                                               Integer qywxStatus, Integer wechatStatus,
                                               Integer dingtalkStatus, Integer feishuStatus);

    /**
     * 🔥 新增：批量更新指定代理器下所有手机设备的状态
     *
     * @param machineId 代理器ID
     * @param status 目标状态
     * @return 更新的设备数量
     */
    int batchUpdateStatusByMachineId(String machineId, Integer status);

}