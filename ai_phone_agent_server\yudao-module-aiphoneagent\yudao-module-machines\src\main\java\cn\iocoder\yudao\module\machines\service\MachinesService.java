package cn.iocoder.yudao.module.machines.service;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.machines.controller.admin.vo.*;
import cn.iocoder.yudao.module.machines.dal.dataobject.MachinesDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 代理器管理 Service 接口
 *
 * <AUTHOR>
 */
public interface MachinesService {

    /**
     * 更新代理器
     *
     * @param updateReqVO 更新信息
     */
    void updateMachines(@Valid MachinesUpdateReqVO updateReqVO);

    /**
     * 删除代理器
     *
     * @param id 主键ID
     */
    void deleteMachines(Long id);

    /**
     * 批量删除代理器
     *
     * @param ids 主键ID列表
     */
    void deleteMachinesList(List<Long> ids);

    /**
     * 获得代理器
     *
     * @param id 主键ID
     * @return 代理器
     */
    MachinesDO getMachines(Long id);

    /**
     * 获得代理器分页
     *
     * @param pageReqVO 分页查询
     * @return 代理器分页
     */
    PageResult<MachinesDO> getMachinesPage(MachinesPageReqVO pageReqVO);

    /**
     * 获得代理器列表
     *
     * @return 代理器列表
     */
    List<MachinesDO> getMachinesList();

    /**
     * 暂停代理器
     *
     * @param id 代理器ID
     */
    void pauseMachines(Long id);

    /**
     * 恢复代理器
     *
     * @param id 代理器ID
     */
    void resumeMachines(Long id);

    /**
     * 更新代理器的最后上报时间
     *
     * @param machineId 机器ID
     */
    void updateLastReportTime(String machineId);

    /**
     * 🔥 新增：更新代理器状态
     * 用于离线检测定时任务同步状态
     *
     * @param id 代理器主键ID
     * @param status 新状态（0-运行中，1-离线，2-异常，3-暂停）
     */
    void updateMachineStatus(Long id, Integer status);

    /**
     * 🔥 新增：根据代理器ID更新状态
     * 用于离线检测定时任务同步状态
     *
     * @param machineId 代理器ID
     * @param status 新状态（0-运行中，1-离线，2-异常，3-暂停）
     */
    void updateMachineStatusByMachineId(String machineId, Integer status);

    /**
     * 🔥 新增：获取所有活跃状态的代理器
     * 用于离线检测定时任务
     *
     * @return 活跃状态的代理器列表（排除已删除的）
     */
    List<MachinesDO> getActiveMachines();

    /**
     * 注册代理器
     *
     * @param machineId 机器码
     * @param osType 操作系统类型
     * @param tenantId 租户ID
     * @param cloudDomain 云管理端域名
     * @return 代理器信息
     */
    MachinesDO registerMachines(String machineId, String osType, Long tenantId, String cloudDomain);

    /**
     * 查询代理器状态（忽略租户隔离，用于客户端查询）
     *
     * @param machineId 机器码
     * @return 代理器信息
     */
    MachinesDO queryMachinesStatus(String machineId);

    /**
     * 根据machineId获取代理器（遵循租户隔离）
     *
     * @param machineId 机器码
     * @return 代理器信息
     */
    MachinesDO getMachinesByMachineId(String machineId);

    /**
     * 客户端查询代理器和设备状态（忽略租户隔离）
     *
     * @param machineId 机器码
     * @return 代理器状态及其下所有手机设备状态
     */
    cn.iocoder.yudao.module.machines.controller.admin.vo.MachineStatusRespVO queryClientStatus(String machineId);

    /**
     * 更新代理器信息
     *
     * @param machineId 机器码
     * @param tenantId 租户ID
     * @param cloudDomain 云管理端域名
     * @param osType 操作系统类型
     * @return 代理器信息
     */
    MachinesDO updateMachinesInfo(String machineId, Long tenantId, String cloudDomain, String osType);

} 