package cn.iocoder.yudao.module.machines.service;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.time.LocalDateTime;
import cn.iocoder.yudao.module.machines.controller.admin.vo.*;
import cn.iocoder.yudao.module.machines.dal.dataobject.MachinesDO;
import cn.iocoder.yudao.module.machines.dal.mysql.MachinesMapper;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.machines.enums.MachinesStatusEnum;

import cn.iocoder.yudao.module.machines.enums.ErrorCodeConstants;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.machines.event.MachineStatusChangeEvent;
import cn.iocoder.yudao.module.machines.event.MachineDeletedEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationContext;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 代理器管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class MachinesServiceImpl implements MachinesService {

    @Resource
    private MachinesMapper machinesMapper;

    // 🔥 新增：事件发布器，用于发布状态变更事件
    @Resource
    private ApplicationEventPublisher eventPublisher;

    // 🔥 新增：租户服务，用于验证租户是否存在
    @Resource
    private cn.iocoder.yudao.module.system.service.tenant.TenantService tenantService;

    // 🔥 新增：应用上下文，用于动态获取手机设备服务
    @Resource
    private ApplicationContext applicationContext;



    @Override
    public void updateMachines(MachinesUpdateReqVO updateReqVO) {
        // 校验存在
        validateMachinesExists(updateReqVO.getId());
        
        // 更新
        MachinesDO updateObj = BeanUtils.toBean(updateReqVO, MachinesDO.class);
        machinesMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMachines(Long id) {
        // 校验存在并获取代理器信息
        MachinesDO machine = validateMachinesExistsAndGet(id);

        // 🔥 物理删除代理器（数据库外键约束会自动级联删除关联的手机设备）
        machinesMapper.deleteById(id);

        log.info("删除代理器成功（级联删除关联手机设备），ID: {}, machineId: {}, name: {}",
            id, machine.getMachineId(), machine.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMachinesList(List<Long> ids) {
        // 批量物理删除代理器（数据库外键约束会自动级联删除关联的手机设备）
        for (Long id : ids) {
            // 获取代理器信息用于日志
            MachinesDO machine = validateMachinesExistsAndGet(id);

            // 删除代理器
            machinesMapper.deleteById(id);

            log.info("批量删除代理器成功（级联删除关联手机设备），ID: {}, machineId: {}, name: {}",
                id, machine.getMachineId(), machine.getName());
        }
    }

    private void validateMachinesExists(Long id) {
        if (machinesMapper.selectById(id) == null) {
            throw exception(ErrorCodeConstants.MACHINES_NOT_EXISTS);
        }
    }

    // 🔥 新增：校验代理器存在并返回对象
    private MachinesDO validateMachinesExistsAndGet(Long id) {
        MachinesDO machine = machinesMapper.selectById(id);
        if (machine == null) {
            throw exception(ErrorCodeConstants.MACHINES_NOT_EXISTS);
        }
        return machine;
    }

    // 🔥 新增：验证目标租户是否存在和有效
    private boolean validateTargetTenant(Long tenantId) {
        try {
            // 基本参数验证
            if (tenantId == null || tenantId <= 0) {
                log.warn("[validateTargetTenant] 租户ID无效: {}", tenantId);
                return false;
            }

            // 🔥 使用TenantUtils.executeIgnore来跨租户查询租户信息
            return TenantUtils.executeIgnore(() -> {
                try {
                    // 🔥 调用租户服务验证租户是否存在
                    cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO tenant = tenantService.getTenant(tenantId);

                    if (tenant == null) {
                        log.warn("[validateTargetTenant] 租户不存在: {}", tenantId);
                        return false;
                    }

                    // 🔥 验证租户状态是否正常
                    if (tenant.getStatus() == null || !tenant.getStatus().equals(cn.iocoder.yudao.framework.common.enums.CommonStatusEnum.ENABLE.getStatus())) {
                        log.warn("[validateTargetTenant] 租户状态异常: tenantId={}, status={}", tenantId, tenant.getStatus());
                        return false;
                    }

                    // 🔥 验证租户是否过期
                    if (tenant.getExpireTime() != null && tenant.getExpireTime().isBefore(java.time.LocalDateTime.now())) {
                        log.warn("[validateTargetTenant] 租户已过期: tenantId={}, expireTime={}", tenantId, tenant.getExpireTime());
                        return false;
                    }

                    log.info("[validateTargetTenant] 租户验证通过: tenantId={}, name={}, status={}",
                            tenantId, tenant.getName(), tenant.getStatus());
                    return true;

                } catch (Exception e) {
                    log.error("[validateTargetTenant] 查询租户信息时发生异常: tenantId={}, error={}", tenantId, e.getMessage());
                    return false;
                }
            });

        } catch (Exception e) {
            log.error("[validateTargetTenant] 验证租户 {} 时发生异常: {}", tenantId, e.getMessage());
            return false;
        }
    }

    @Override
    public MachinesDO getMachines(Long id) {
        return machinesMapper.selectById(id);
    }

    @Override
    public PageResult<MachinesDO> getMachinesPage(MachinesPageReqVO pageReqVO) {
        return machinesMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MachinesDO> getMachinesList() {
        return machinesMapper.selectList();
    }

    @Override
    public void pauseMachines(Long id) {
        // 校验存在并获取代理器信息
        MachinesDO machine = validateMachinesExistsAndGet(id);
        Integer oldStatus = machine.getStatus();

        // 更新状态为暂停
        MachinesDO updateObj = new MachinesDO();
        updateObj.setId(id);
        updateObj.setStatus(MachinesStatusEnum.PAUSED.getStatus());
        machinesMapper.updateById(updateObj);

        // 🔥 新增：发布状态变更事件，通知其他模块同步状态
        publishStatusChangeEvent(machine.getMachineId(), MachinesStatusEnum.PAUSED.getStatus(), oldStatus, "手动暂停");

        log.info("代理器已暂停，已发布状态变更事件: machineId={}", machine.getMachineId());
    }

    @Override
    public void resumeMachines(Long id) {
        // 校验存在并获取代理器信息
        MachinesDO machine = validateMachinesExistsAndGet(id);
        Integer oldStatus = machine.getStatus();

        // 更新状态为运行中
        MachinesDO updateObj = new MachinesDO();
        updateObj.setId(id);
        updateObj.setStatus(MachinesStatusEnum.RUNNING.getStatus());
        machinesMapper.updateById(updateObj);

        // 🔥 新增：发布状态变更事件，通知其他模块同步状态
        publishStatusChangeEvent(machine.getMachineId(), MachinesStatusEnum.RUNNING.getStatus(), oldStatus, "手动启用");

        log.info("代理器已启用，已发布状态变更事件: machineId={}", machine.getMachineId());
    }

    @Override
    public void updateLastReportTime(String machineId) {
        machinesMapper.updateLastReportTimeByMachineId(machineId);
    }

    @Override
    public void updateMachineStatus(Long id, Integer status) {
        MachinesDO updateObj = new MachinesDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        machinesMapper.updateById(updateObj);
        log.info("🔥 离线检测：更新代理器状态 id={}, status={}", id, status);
    }

    @Override
    public void updateMachineStatusByMachineId(String machineId, Integer status) {
        MachinesDO machine = machinesMapper.selectByMachineId(machineId);
        if (machine != null) {
            updateMachineStatus(machine.getId(), status);
        }
    }

    @Override
    public List<MachinesDO> getActiveMachines() {
        // 查询所有未删除的代理器
        return machinesMapper.selectList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MachinesDO registerMachines(String machineId, String osType, Long tenantId, String cloudDomain) {
        // 检查是否已存在
        MachinesDO existingMachine = machinesMapper.selectByMachineId(machineId);
        if (existingMachine != null) {
            // 如果已存在，更新最后上报时间
            updateLastReportTime(machineId);
            log.info("代理器已存在，更新上报时间: machineId={}", machineId);
            return existingMachine;
        }

        // 创建新的代理器记录
        log.info("创建新的代理器记录: machineId={}", machineId);
        MachinesDO machines = new MachinesDO();
        machines.setMachineId(machineId);
        machines.setOsType(osType);
        machines.setStatus(MachinesStatusEnum.RUNNING.getStatus());
        machines.setRunDays(0);
        machines.setRegisterTime(LocalDateTime.now());
        machines.setLastReportTime(LocalDateTime.now());
        machines.setTenantId(tenantId);
        machines.setCloudDomain(cloudDomain);

        // 🔥 手动设置基础字段，因为PhysicalDeleteBaseDO的自动填充可能不工作
        LocalDateTime now = LocalDateTime.now();
        machines.setCreateTime(now);
        machines.setUpdateTime(now);
        machines.setCreator("system");
        machines.setUpdater("system");

        machinesMapper.insert(machines);

        log.info("代理器注册成功: machineId={}, id={}", machineId, machines.getId());
        return machines;
    }

    @Override
    public MachinesDO queryMachinesStatus(String machineId) {
        log.info("[queryMachinesStatus] 开始查询代理器状态，machineId: {}", machineId);
        // 使用 TenantUtils.executeIgnore 来忽略租户隔离
        MachinesDO result = TenantUtils.executeIgnore(() -> {
            return machinesMapper.selectByMachineId(machineId);
        });
        log.info("[queryMachinesStatus] 查询结果: {}", result);
        return result;
    }

    @Override
    public MachinesDO getMachinesByMachineId(String machineId) {
        return machinesMapper.selectByMachineId(machineId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MachinesDO updateMachinesInfo(String machineId, Long tenantId, String cloudDomain, String osType) {
        log.info("[updateMachinesInfo] 开始更新代理器信息，machineId: {}, tenantId: {}, cloudDomain: {}, osType: {}", machineId, tenantId, cloudDomain, osType);

        // 先跨租户查询代理器是否存在
        MachinesDO existingMachine = TenantUtils.executeIgnore(() -> {
            return machinesMapper.selectByMachineId(machineId);
        });
        log.info("[updateMachinesInfo] 查询到的现有代理器: {}", existingMachine);

        if (existingMachine == null) {
            log.warn("[updateMachinesInfo] 代理器不存在，machineId: {}", machineId);
            throw exception(ErrorCodeConstants.MACHINES_NOT_EXISTS);
        }

        // 🔥 优化：支持租户迁移，允许修改租户ID
        boolean tenantChanged = false;
        if (!existingMachine.getTenantId().equals(tenantId)) {
            log.info("[updateMachinesInfo] 检测到租户迁移，从租户 {} 迁移到租户 {}", existingMachine.getTenantId(), tenantId);

            // 🔥 验证目标租户是否存在和有效
            boolean tenantValid = validateTargetTenant(tenantId);
            if (!tenantValid) {
                log.warn("[updateMachinesInfo] 目标租户 {} 不存在或无效，拒绝迁移", tenantId);
                throw exception(ErrorCodeConstants.MACHINES_NOT_EXISTS);
            }

            log.info("[updateMachinesInfo] 租户迁移验证通过，允许从租户 {} 迁移到租户 {}", existingMachine.getTenantId(), tenantId);
            tenantChanged = true;
        }

        // 更新代理器信息
        MachinesDO updateObj = new MachinesDO();
        updateObj.setId(existingMachine.getId());
        updateObj.setTenantId(tenantId);
        updateObj.setCloudDomain(cloudDomain);
        updateObj.setOsType(osType);  // 添加操作系统类型更新
        updateObj.setLastReportTime(LocalDateTime.now());

        machinesMapper.updateById(updateObj);

        // 🔥 新增：如果租户ID发生变化，同步更新关联手机设备的租户ID
        if (tenantChanged) {
            try {
                log.info("[updateMachinesInfo] 开始同步更新关联手机设备的租户ID，代理器ID: {}", machineId);

                // 动态获取手机设备服务，避免循环依赖
                Object mobileDeviceService = applicationContext.getBean("mobileDeviceServiceImpl");

                // 使用反射调用方法
                java.lang.reflect.Method updateMethod = mobileDeviceService.getClass()
                    .getMethod("updateTenantIdByMachineId", String.class, Long.class);
                int updatedDeviceCount = (Integer) updateMethod.invoke(mobileDeviceService, machineId, tenantId);

                log.info("[updateMachinesInfo] 关联手机设备租户ID同步完成，代理器ID: {}, 更新设备数量: {}",
                    machineId, updatedDeviceCount);
            } catch (Exception e) {
                log.error("[updateMachinesInfo] 同步手机设备租户ID失败，代理器ID: {}, 错误: {}",
                    machineId, e.getMessage(), e);
                // 注意：这里不抛出异常，避免影响代理器更新的主流程
                // 但会记录错误日志，便于后续排查和手动修复
            }
        }

        // 返回更新后的信息
        return machinesMapper.selectById(existingMachine.getId());
    }

    /**
     * 🔥 新增：发布状态变更事件
     * 当代理器状态变化时，发布事件通知其他模块进行相应处理
     */
    private void publishStatusChangeEvent(String machineId, Integer newStatus, Integer oldStatus, String reason) {
        try {
            log.info("发布代理器状态变更事件: machineId={}, newStatus={}, oldStatus={}, reason={}",
                machineId, newStatus, oldStatus, reason);

            // 创建并发布状态变更事件
            MachineStatusChangeEvent event = new MachineStatusChangeEvent(
                this, machineId, newStatus, oldStatus, reason);
            eventPublisher.publishEvent(event);

            log.debug("代理器状态变更事件发布成功: machineId={}", machineId);

        } catch (Exception e) {
            log.error("发布代理器状态变更事件失败: machineId={}, newStatus={}, error={}",
                machineId, newStatus, e.getMessage(), e);
            // 不抛出异常，避免影响代理器状态更新
        }
    }

    @Override
    public MachineStatusRespVO queryClientStatus(String machineId) {
        // 🔥 新增：客户端查询代理器和设备状态
        try {
            // 查询代理器状态（跨租户查询）
            MachinesDO machine = TenantUtils.execute(null, () -> {
                return machinesMapper.selectByMachineId(machineId);
            });

            if (machine == null) {
                return null;
            }

            // 构建代理器状态响应
            MachineStatusRespVO respVO = new MachineStatusRespVO();
            respVO.setMachineId(machine.getMachineId());
            respVO.setStatus(machine.getStatus());
            respVO.setStatusName(MachinesStatusEnum.valueOf(machine.getStatus()).getName());
            respVO.setLastReportTime(machine.getLastReportTime());

            // 🔥 查询该代理器下的所有手机设备状态
            List<MachineStatusRespVO.MobileDeviceStatus> mobileDevices = queryMobileDevicesByMachineId(machineId);
            respVO.setMobileDevices(mobileDevices);

            return respVO;
        } catch (Exception e) {
            log.error("查询客户端状态失败: machineId={}, error={}", machineId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 查询指定代理器下的所有手机设备状态
     */
    private List<MachineStatusRespVO.MobileDeviceStatus> queryMobileDevicesByMachineId(String machineId) {
        try {
            // 🔥 这里需要调用手机设备服务查询设备列表
            // 由于跨模块调用，我们先返回空列表，后续通过依赖注入手机设备服务来实现
            log.info("查询代理器 {} 下的手机设备状态", machineId);
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("查询手机设备状态失败: machineId={}, error={}", machineId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

}