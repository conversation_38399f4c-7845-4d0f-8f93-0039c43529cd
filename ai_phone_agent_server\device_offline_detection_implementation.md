# 🔧 设备离线检测功能实现

## 📋 问题分析

### **原问题**
- **monitor模块**：能通过Redis TTL正确判断离线状态
- **machines模块**：数据库状态字段没有同步更新为离线
- **mobile模块**：数据库状态字段没有同步更新为离线
- **前端查询**：看不到正确的离线状态

### **解决方案**
采用**定时任务 + 实时同步**的组合方案，确保数据库状态与Redis心跳状态保持一致。

## 🔧 实现内容

### **1. MachinesService接口扩展**
**文件**: `MachinesService.java`

新增方法：
```java
// 更新代理器状态
void updateMachineStatus(Long id, Integer status);
void updateMachineStatusByMachineId(String machineId, Integer status);

// 获取活跃代理器列表
List<MachinesDO> getActiveMachines();
```

### **2. MachinesServiceImpl实现**
**文件**: `MachinesServiceImpl.java`

实现状态更新逻辑：
```java
@Override
public void updateMachineStatus(Long id, Integer status) {
    MachinesDO updateObj = new MachinesDO();
    updateObj.setId(id);
    updateObj.setStatus(status);
    machinesMapper.updateById(updateObj);
}
```

### **3. MobileDeviceService接口扩展**
**文件**: `MobileDeviceService.java`

新增方法：
```java
// 更新手机设备状态
void updateDeviceStatus(Long id, Integer status);
void updateDeviceStatusByDeviceId(String deviceId, Integer status);

// 获取活跃设备列表
List<MobileDeviceDO> getActiveDevices();
```

### **4. MobileDeviceServiceImpl实现**
**文件**: `MobileDeviceServiceImpl.java`

实现状态更新逻辑：
```java
@Override
public void updateDeviceStatus(Long id, Integer status) {
    MobileDeviceDO updateObj = new MobileDeviceDO();
    updateObj.setId(id);
    updateObj.setStatus(status);
    mobileDeviceMapper.updateById(updateObj);
}
```

### **5. 离线检测定时任务**
**文件**: `DeviceOfflineCheckJob.java`

核心功能：
- **执行频率**：每分钟执行一次
- **检测逻辑**：检查Redis心跳 + 数据库时间备用判断
- **状态更新**：自动将离线设备状态同步到数据库

```java
@Scheduled(fixedRate = 60000) // 每分钟执行一次
public void checkAndUpdateOfflineDevices() {
    // 1. 检查代理器离线状态
    int offlineMachineCount = checkMachineOfflineStatus();
    
    // 2. 检查手机设备离线状态
    int offlineDeviceCount = checkMobileDeviceOfflineStatus();
}
```

### **6. 实时在线状态同步**
**文件**: `DeviceServiceImpl.java`

在设备上报时同步在线状态：
```java
// 🔥 新增：同步在线状态到数据库
try {
    // 确保代理器状态为运行中（如果不是暂停状态）
    syncMachineOnlineStatus(deviceInfo.getMachine());
    
    // 确保手机设备状态为在线（如果不是暂停状态）
    syncDeviceOnlineStatus(deviceInfo.getDevice());
} catch (Exception e) {
    log.warn("同步设备在线状态到数据库失败");
}
```

## 📊 状态码定义

### **代理器状态（machines表）**
- `0` - 运行中
- `1` - 离线
- `2` - 异常
- `3` - 暂停

### **手机设备状态（mobile_devices表）**
- `0` - 启用
- `1` - 暂停
- `2` - 离线

## 🔄 工作流程

### **离线检测流程**
```
1. 定时任务每分钟执行
2. 查询所有活跃设备
3. 检查Redis心跳状态
4. 如果无心跳 && 非暂停状态 -> 更新为离线
5. 记录离线设备日志
```

### **在线恢复流程**
```
1. 客户端上报设备状态
2. 更新Redis心跳
3. 检查数据库当前状态
4. 如果是离线状态 -> 更新为在线
5. 记录恢复在线日志
```

### **状态优先级**
```
暂停状态 > 在线状态 > 离线状态
```

## 🧪 测试验证

### **测试场景1：正常离线检测**
1. **启动客户端**：正常上报，状态为"运行中"/"启用"
2. **停止客户端**：停止上报
3. **等待2分钟**：Redis TTL过期
4. **检查数据库**：状态应更新为"离线"
5. **前端查询**：应显示"离线"状态

### **测试场景2：在线恢复**
1. **客户端离线**：数据库状态为"离线"
2. **重启客户端**：开始上报
3. **检查数据库**：状态应更新为"运行中"/"启用"
4. **前端查询**：应显示"在线"状态

### **测试场景3：暂停状态保护**
1. **手动暂停设备**：设置状态为"暂停"
2. **客户端离线**：停止上报
3. **等待离线检测**：状态应保持"暂停"
4. **客户端恢复**：开始上报
5. **检查状态**：应保持"暂停"（不会自动恢复）

### **验证SQL查询**
```sql
-- 查看代理器状态
SELECT machine_id, name, status, last_report_time 
FROM machines 
WHERE status = 1; -- 离线状态

-- 查看手机设备状态
SELECT device_id, name, status, last_report_time 
FROM mobile_devices 
WHERE status = 2; -- 离线状态
```

### **验证Redis状态**
```bash
# 检查代理器心跳
redis-cli GET "device:monitor:machine:{machineId}"

# 检查设备心跳
redis-cli HGETALL "device:monitor:mobile:{deviceId}"
```

## 📝 日志监控

### **关键日志**
```
🔍 开始执行设备离线检测定时任务
🔴 代理器离线状态同步: machineId=xxx, name=xxx
🔴 手机设备离线状态同步: deviceId=xxx, name=xxx
✅ 设备离线检测完成，离线代理器: 2, 离线手机设备: 5
🟢 代理器在线状态同步: machineId=xxx, 状态: 离线 -> 运行中
🟢 手机设备在线状态同步: deviceId=xxx, 状态: 离线 -> 启用
```

### **监控指标**
- 离线检测任务执行频率
- 每次检测到的离线设备数量
- 在线恢复的设备数量
- 任务执行耗时

## ⚠️ 注意事项

### **1. 性能考虑**
- 定时任务每分钟执行，不会频繁操作数据库
- 只更新状态变化的设备，减少不必要的数据库写入
- 使用批量查询，避免N+1问题

### **2. 数据一致性**
- Redis TTL时间：120秒
- 定时任务间隔：60秒
- 确保离线检测及时性

### **3. 状态保护**
- 暂停状态不会被自动修改
- 只在状态确实需要变更时才更新数据库
- 异常情况下有日志记录和容错处理

## ✅ 预期效果

### **修复前**
- ❌ monitor模块能判断离线，但数据库状态不同步
- ❌ 前端查询显示错误状态
- ❌ 离线设备仍显示为"运行中"

### **修复后**
- ✅ monitor模块判断离线 + 数据库状态同步
- ✅ 前端查询显示正确的离线状态
- ✅ 离线设备正确显示为"离线"
- ✅ 在线恢复时自动更新状态

**现在machines和mobile模块的数据库状态会与Redis心跳状态保持一致，前端查询时能看到正确的离线状态！** ✅
