<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="auto"
    >
      <el-form-item label="手机标识" prop="deviceId">
        <el-input v-model="queryParams.deviceId" clearable placeholder="请输入手机标识" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="手机名称" prop="name">
        <el-input v-model="queryParams.name" clearable placeholder="请输入手机名称" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="所属代理器" prop="machineId">
        <el-select v-model="queryParams.machineId" clearable placeholder="请选择代理器" style="min-width: 150px;">
          <el-option v-for="machine in machinesList" :key="machine.machineId" :label="machine.name" :value="machine.machineId" />
        </el-select>
      </el-form-item>
      <el-form-item label="操作系统型号" prop="osModel">
        <el-input v-model="queryParams.osModel" clearable placeholder="请输入操作系统型号" />
      </el-form-item>
      <el-form-item label="卡1手机号" prop="simCard1">
        <el-input v-model="queryParams.simCard1" clearable placeholder="请输入卡1手机号" />
      </el-form-item>
      <el-form-item label="卡2手机号" prop="simCard2">
        <el-input v-model="queryParams.simCard2" clearable placeholder="请输入卡2手机号" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" clearable placeholder="请选择状态" style="min-width: 150px;">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="抖音状态" prop="douyinStatus">
        <el-select v-model="queryParams.douyinStatus" clearable placeholder="请选择抖音状态" style="min-width: 150px;">
          <el-option v-for="item in appStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="小红书状态" prop="xiaohongshuStatus">
        <el-select v-model="queryParams.xiaohongshuStatus" clearable placeholder="请选择小红书状态" style="min-width: 150px;">
          <el-option v-for="item in appStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="视频号状态" prop="videoStatus">
        <el-select v-model="queryParams.videoStatus" clearable placeholder="请选择视频号状态" style="min-width: 150px;">
          <el-option v-for="item in appStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="公众号状态" prop="officialAccountStatus">
        <el-select v-model="queryParams.officialAccountStatus" clearable placeholder="请选择公众号状态" style="min-width: 150px;">
          <el-option v-for="item in appStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="企微状态" prop="qywxStatus">
        <el-select v-model="queryParams.qywxStatus" clearable placeholder="请选择企微状态" style="min-width: 150px;">
          <el-option v-for="item in appStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="微信状态" prop="wechatStatus">
        <el-select v-model="queryParams.wechatStatus" clearable placeholder="请选择微信状态" style="min-width: 150px;">
          <el-option v-for="item in appStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="钉钉状态" prop="dingtalkStatus">
        <el-select v-model="queryParams.dingtalkStatus" clearable placeholder="请选择钉钉状态" style="min-width: 150px;">
          <el-option v-for="item in appStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="飞书状态" prop="feishuStatus">
        <el-select v-model="queryParams.feishuStatus" clearable placeholder="请选择飞书状态" style="min-width: 150px;">
          <el-option v-for="item in appStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <!-- 🔥 新增：显示全部按钮，用于清除来源过滤 -->
        <el-button
          v-if="isFromMachines"
          @click="showAllDevices"
          type="info"
          plain
        >
          <Icon class="mr-5px" icon="ep:view" />
          显示全部设备
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" stripe style="width: 100%" table-layout="auto">
      <el-table-column align="center" label="手机标识" prop="deviceId" width="100" />
      <el-table-column align="center" label="手机名称" prop="name" width="100">
        <template #default="scope">
          <span v-if="scope.row.name">{{ scope.row.name }}</span>
          <span v-else class="text-gray-400">手机未命名</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="所属代理器" prop="machineName" width="120">
        <template #default="scope">
          <span v-if="scope.row.machineName">{{ scope.row.machineName }}</span>
          <span v-else class="text-gray-400">代理器未命名</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作系统型号" prop="osModel" width="120" />
      <el-table-column align="center" label="卡1手机号" prop="simCard1" width="100" />
      <el-table-column align="center" label="卡2手机号" prop="simCard2" width="100" />
      <!-- 🔥 设备状态列 -->
      <el-table-column align="center" label="设备状态" prop="status" width="100">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ getStatusName(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <!-- 🔥 应用状态详细列 -->
      <el-table-column align="center" label="抖音状态" width="100">
        <template #default="scope">
          <el-tag :type="getAppStatusTagType(scope.row.douyinStatus)" size="small">
            {{ getAppStatusName(scope.row.douyinStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="小红书状态" width="100">
        <template #default="scope">
          <el-tag :type="getAppStatusTagType(scope.row.xiaohongshuStatus)" size="small">
            {{ getAppStatusName(scope.row.xiaohongshuStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="企微状态" width="100">
        <template #default="scope">
          <el-tag :type="getAppStatusTagType(scope.row.qywxStatus)" size="small">
            {{ getAppStatusName(scope.row.qywxStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="微信状态" width="100">
        <template #default="scope">
          <el-tag :type="getAppStatusTagType(scope.row.wechatStatus)" size="small">
            {{ getAppStatusName(scope.row.wechatStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="视频号状态" width="100">
        <template #default="scope">
          <el-tag :type="getAppStatusTagType(scope.row.videoStatus)" size="small">
            {{ getAppStatusName(scope.row.videoStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="公众号状态" width="100">
        <template #default="scope">
          <el-tag :type="getAppStatusTagType(scope.row.officialAccountStatus)" size="small">
            {{ getAppStatusName(scope.row.officialAccountStatus) }}
          </el-tag>
        </template>
      </el-table-column>


      <el-table-column align="center" label="钉钉状态" width="100">
        <template #default="scope">
          <el-tag :type="getAppStatusTagType(scope.row.dingtalkStatus)" size="small">
            {{ getAppStatusName(scope.row.dingtalkStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="飞书状态" width="100">
        <template #default="scope">
          <el-tag :type="getAppStatusTagType(scope.row.feishuStatus)" size="small">
            {{ getAppStatusName(scope.row.feishuStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="100">
        <template #default="scope">
          <!-- 🔥 修复：根据状态显示正确的操作按钮 -->
          <el-button
            v-hasPermi="['aiphoneagent:mobile:pause']"
            link
            type="warning"
            @click="handlePause(scope.row.id)"
            v-if="scope.row.status === 0"
            size="small"
          >暂停</el-button>
          <el-button
            v-hasPermi="['aiphoneagent:mobile:enable']"
            link
            type="success"
            @click="handleEnable(scope.row.id)"
            v-if="scope.row.status === 1"
            size="small"
          >启用</el-button>

          <!-- 🔥 重构：移除删除按钮，手机设备只能通过删除代理器来级联删除 -->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>




</template>

<script lang="ts" setup>
import * as MobileDeviceApi from '@/api/aiphoneagent/mobile'

defineOptions({ name: 'AiphoneagentMobile' })

const message = useMessage()

const loading = ref(true)
const total = ref(0)
const list = ref([])
const machinesList = ref<MobileDeviceApi.MachineOption[]>([])

// 🔥 新增：标识是否从代理器页面跳转过来
const isFromMachines = ref(false)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  deviceId: undefined as string | undefined,
  name: undefined as string | undefined,
  machineId: undefined as string | undefined,
  osModel: undefined as string | undefined,
  simCard1: undefined as string | undefined,
  simCard2: undefined as string | undefined,
  status: undefined as number | undefined,
  douyinStatus: undefined as number | undefined,
  xiaohongshuStatus: undefined as number | undefined,
  videoStatus: undefined as number | undefined,
  officialAccountStatus: undefined as number | undefined,
  qywxStatus: undefined as number | undefined,
  wechatStatus: undefined as number | undefined,
  dingtalkStatus: undefined as number | undefined,
  feishuStatus: undefined as number | undefined,
  createTime: [] as string[]
})
const queryFormRef = ref()

const statusOptions = [
  { value: 0, label: '启用' },
  { value: 1, label: '暂停' }
]
const appStatusOptions = [
  { value: 0, label: '未安装' },
  { value: 1, label: '异常' },
  { value: 2, label: '运行中' }
]
// 🔥 新增：状态相关方法
const getStatusTagType = (status: number) => {
  switch (status) {
    case 0: return 'success'  // 启用 - 绿色
    case 1: return 'warning'  // 暂停 - 橙色
    default: return 'primary'
  }
}

const getStatusName = (status: number) => {
  switch (status) {
    case 0: return '启用'
    case 1: return '暂停'
    default: return '未知'
  }
}

// 🔥 新增：应用状态相关方法
const getAppStatusTagType = (status: number) => {
  switch (status) {
    case 0: return 'info'     // 未安装 - 灰色
    case 1: return 'danger'   // 异常 - 红色
    case 2: return 'success'  // 运行中 - 绿色
    default: return 'info'
  }
}

const getAppStatusName = (status: number) => {
  switch (status) {
    case 0: return '未安装'
    case 1: return '异常'
    case 2: return '运行中'
    default: return '未知'
  }
}





/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await MobileDeviceApi.getMobileDevicePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 获取代理器列表 */
const getMachinesList = async () => {
  try {
    machinesList.value = await MobileDeviceApi.getMachinesListForSelect()
  } catch (error) {
    console.error('获取代理器列表失败:', error)
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 🔥 新增：显示全部设备（清除来源过滤） */
const showAllDevices = () => {
  // 清除来源标识
  isFromMachines.value = false
  // 清除sessionStorage中的过滤信息
  sessionStorage.removeItem('mobileFilterMachineId')
  sessionStorage.removeItem('mobileFilterFrom')
  // 清空所有查询条件
  queryFormRef.value.resetFields()
  // 重新加载数据（不需要修改URL，因为URL本身就是统一的）
  handleQuery()
}



/** 暂停操作 */
const handlePause = async (id: number) => {
  try {
    await message.confirm('确认要暂停该手机设备吗？')
    await MobileDeviceApi.pauseMobileDevice(id)
    message.success('已暂停')
    await getList() // 🔥 修复：使用await确保数据刷新
  } catch (error) {
    console.error('暂停失败:', error)
  }
}

/** 启用操作 */
const handleEnable = async (id: number) => {
  try {
    await message.confirm('确认要启用该手机设备吗？')
    await MobileDeviceApi.enableMobileDevice(id)
    message.success('已启用')
    await getList() // 🔥 修复：使用await确保数据刷新
  } catch (error) {
    console.error('启用失败:', error)
  }
}

// 🔥 重构：移除删除功能，手机设备只能通过删除代理器来级联删除

/** 🔥 优化：从sessionStorage读取过滤信息，避免URL查询参数 */
const initializeFromRoute = () => {
  // 从sessionStorage读取过滤信息
  const machineId = sessionStorage.getItem('mobileFilterMachineId')
  const from = sessionStorage.getItem('mobileFilterFrom')

  console.log('🔥 手机管理页面初始化，sessionStorage数据:', { machineId, from })

  // 如果是从代理器页面跳转过来的，应用过滤条件
  if (from === 'machines' && machineId) {
    isFromMachines.value = true
    queryParams.machineId = machineId
    console.log('🔥 从代理器页面跳转，应用过滤条件:', machineId)
    console.log('🔥 isFromMachines设置为:', isFromMachines.value)

    // 🔥 重要：应用过滤后立即清除sessionStorage，避免影响后续访问
    sessionStorage.removeItem('mobileFilterMachineId')
    sessionStorage.removeItem('mobileFilterFrom')
  }
  // 如果是直接访问或从菜单进入，不应用任何过滤条件
  else {
    isFromMachines.value = false
    // 清空所有查询条件，显示全部数据
    queryParams.machineId = ''
    queryParams.deviceId = ''
    queryParams.name = ''
    queryParams.osModel = ''
    queryParams.simCard1 = ''
    queryParams.simCard2 = ''
    queryParams.status = undefined
    queryParams.douyinStatus = undefined
    queryParams.xiaohongshuStatus = undefined
    queryParams.wechatStatus = undefined
    queryParams.qywxStatus = undefined
    queryParams.dingtalkStatus = undefined
    queryParams.feishuStatus = undefined
    console.log('🔥 直接访问或菜单进入，显示全部数据')
    console.log('🔥 isFromMachines设置为:', isFromMachines.value)
  }
}

/** 初始化 **/
onMounted(() => {
  getMachinesList()
  // 🔥 优化：智能处理URL参数
  initializeFromRoute()
  getList()
})

/** 页面激活时刷新数据 */
onActivated(() => {
  getMachinesList()
  // 🔥 修复：页面激活时也需要检查sessionStorage，处理第二次跳转的情况
  // 检查是否有新的跳转参数
  const machineId = sessionStorage.getItem('mobileFilterMachineId')
  const from = sessionStorage.getItem('mobileFilterFrom')

  if (from === 'machines' && machineId) {
    console.log('🔥 页面激活时发现新的跳转参数，重新初始化')
    initializeFromRoute()
  } else {
    console.log('🔥 页面激活时无新参数，保持当前状态')
  }

  getList()
})


</script>

<style lang="scss" scoped>
// 🔥 新增：应用状态概览样式
.app-status-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  align-items: center;

  .el-tag {
    margin: 2px;
  }
}

.text-gray-400 {
  color: #9ca3af;
  font-size: 12px;
}

// 表格优化样式
:deep(.el-table) {
  .el-table__cell {
    padding: 8px 0;
  }

  .el-tag {
    margin: 1px;
  }
}
</style>