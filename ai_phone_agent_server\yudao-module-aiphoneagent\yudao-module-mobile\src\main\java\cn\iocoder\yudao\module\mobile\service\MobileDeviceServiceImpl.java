package cn.iocoder.yudao.module.mobile.service;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.mobile.controller.admin.vo.MobileDevicePageReqVO;
import cn.iocoder.yudao.module.mobile.controller.admin.vo.MobileDeviceSaveReqVO;
import cn.iocoder.yudao.module.mobile.dal.dataobject.MobileDeviceDO;
import cn.iocoder.yudao.module.mobile.dal.mysql.MobileDeviceMapper;
import cn.iocoder.yudao.module.mobile.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.mobile.enums.MobileDeviceStatusEnum;
import cn.iocoder.yudao.module.machines.dal.mysql.MachinesMapper;
import cn.iocoder.yudao.module.channel.websocket.AgentWebSocketEndpoint;

import org.springframework.stereotype.Service;
import jakarta.validation.Valid;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 手机设备管理 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MobileDeviceServiceImpl implements MobileDeviceService {

    @Resource
    private MobileDeviceMapper mobileDeviceMapper;

    @Resource
    private MachinesMapper machinesMapper;

    @Resource
    private AgentWebSocketEndpoint webSocketEndpoint;

    @Override
    public void updateMobileDevice(MobileDeviceSaveReqVO updateReqVO) {
        // 校验手机设备存在
        validateMobileDeviceExists(updateReqVO.getId());
        // 校验设备标识不与其他设备冲突（除了自身）
        validateMobileDeviceIdNotExistsForUpdate(updateReqVO.getId(), updateReqVO.getDeviceId());
        // 校验SIM卡1唯一性（除了自身）
        validateSimCard1NotExistsForUpdate(updateReqVO.getId(), updateReqVO.getSimCard1());
        // 校验SIM卡2唯一性（除了自身）
        validateSimCard2NotExistsForUpdate(updateReqVO.getId(), updateReqVO.getSimCard2());
        // 校验SIM卡1和SIM卡2不能相同
        validateSimCard1NotEqualSimCard2(updateReqVO.getSimCard1(), updateReqVO.getSimCard2());
        // 校验SIM卡1不与任何设备的SIM卡2重复（除了自身）
        validateSimCard1NotExistsInSimCard2ForUpdate(updateReqVO.getId(), updateReqVO.getSimCard1());
        // 校验SIM卡2不与任何设备的SIM卡1重复（除了自身）
        validateSimCard2NotExistsInSimCard1ForUpdate(updateReqVO.getId(), updateReqVO.getSimCard2());
        // 处理machineId：空字符串转换为null
        String machineId = processMachineId(updateReqVO.getMachineId());
        // 校验关联的代理器是否存在（仅当machineId有值时）
        if (machineId != null) {
            validateMachineExists(machineId);
        }
        // 更新
        MobileDeviceDO updateObj = BeanUtils.toBean(updateReqVO, MobileDeviceDO.class);
        updateObj.setMachineId(machineId);

        // 🔥 手动设置更新时间和更新者
        updateObj.setUpdateTime(LocalDateTime.now());
        updateObj.setUpdater("system");

        mobileDeviceMapper.updateById(updateObj);
    }

    // 🔥 重构：移除单独删除手机设备的功能
    // 手机设备只能通过删除代理器来级联删除

    @Override
    public int deleteMobileDevicesByMachineId(String machineId) {
        // 🔥 新增：根据代理器ID删除关联的手机设备（级联删除）
        if (machineId == null || machineId.trim().isEmpty()) {
            return 0;
        }

        // 先查询要删除的设备数量，用于日志记录
        List<MobileDeviceDO> devicesToDelete = mobileDeviceMapper.selectByMachineId(machineId);
        int deleteCount = mobileDeviceMapper.deleteByMachineId(machineId);

        log.info("级联删除手机设备完成，代理器ID: {}, 删除设备数量: {}", machineId, deleteCount);

        return deleteCount;
    }

    @Override
    public MobileDeviceDO getMobileDevice(Long id) {
        return mobileDeviceMapper.selectById(id);
    }

    @Override
    public PageResult<MobileDeviceDO> getMobileDevicePage(MobileDevicePageReqVO pageReqVO) {
        return mobileDeviceMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MobileDeviceDO> getMobileDeviceList() {
        return mobileDeviceMapper.selectList();
    }

    @Override
    public void pauseMobileDevice(Long id) {
        // 校验手机设备存在
        MobileDeviceDO device = validateMobileDeviceExistsAndGet(id);

        // 更新状态为暂停
        MobileDeviceDO updateObj = new MobileDeviceDO();
        updateObj.setId(id);
        updateObj.setStatus(MobileDeviceStatusEnum.PAUSED.getStatus());
        mobileDeviceMapper.updateById(updateObj);

        // 🔥 发送WebSocket指令到客户端暂停指定手机
        sendMobileDeviceControlCommand(device, "pause");

        log.info("手机设备暂停成功，设备ID: {}, 设备标识: {}", id, device.getDeviceId());
    }

    @Override
    public void enableMobileDevice(Long id) {
        // 校验手机设备存在
        MobileDeviceDO device = validateMobileDeviceExistsAndGet(id);

        // 更新状态为启用
        MobileDeviceDO updateObj = new MobileDeviceDO();
        updateObj.setId(id);
        updateObj.setStatus(MobileDeviceStatusEnum.ENABLED.getStatus());
        mobileDeviceMapper.updateById(updateObj);

        // 🔥 发送WebSocket指令到客户端启用指定手机
        sendMobileDeviceControlCommand(device, "enable");

        log.info("手机设备启用成功，设备ID: {}, 设备标识: {}", id, device.getDeviceId());
    }

    @Override
    public List<MobileDeviceDO> getMobileDevicesByMachineId(String machineId) {
        return mobileDeviceMapper.selectByMachineId(machineId);
    }

    @Override
    public void updateLastReportTime(String deviceId) {
        int updateCount = mobileDeviceMapper.updateLastReportTimeByDeviceId(deviceId);
        log.info("🔍 更新移动设备最后上报时间: deviceId={}, 更新行数={}", deviceId, updateCount);

        // 验证更新结果
        MobileDeviceDO device = mobileDeviceMapper.selectByDeviceId(deviceId);
        if (device != null) {
            log.info("🔍 验证更新结果: deviceId={}, 最后上报时间={}", deviceId, device.getLastReportTime());
        } else {
            log.warn("🔍 设备不存在: deviceId={}", deviceId);
        }
    }

    @Override
    public void updateAppStatuses(String deviceId, Integer douyinStatus, Integer xiaohongshuStatus,
                                 Integer videoStatus, Integer officialAccountStatus, Integer qywxStatus,
                                 Integer wechatStatus, Integer dingtalkStatus, Integer feishuStatus) {
        // 获取现有设备信息
        MobileDeviceDO existingDevice = mobileDeviceMapper.selectByDeviceId(deviceId);
        if (existingDevice == null) {
            return; // 设备不存在，不处理
        }

        // 构建更新对象，只更新有变化的状态
        MobileDeviceDO updateObj = new MobileDeviceDO();
        updateObj.setId(existingDevice.getId());

        boolean hasChanges = false;

        if (douyinStatus != null && !douyinStatus.equals(existingDevice.getDouyinStatus())) {
            updateObj.setDouyinStatus(douyinStatus);
            hasChanges = true;
        }
        if (xiaohongshuStatus != null && !xiaohongshuStatus.equals(existingDevice.getXiaohongshuStatus())) {
            updateObj.setXiaohongshuStatus(xiaohongshuStatus);
            hasChanges = true;
        }
        if (videoStatus != null && !videoStatus.equals(existingDevice.getVideoStatus())) {
            updateObj.setVideoStatus(videoStatus);
            hasChanges = true;
        }
        if (officialAccountStatus != null && !officialAccountStatus.equals(existingDevice.getOfficialAccountStatus())) {
            updateObj.setOfficialAccountStatus(officialAccountStatus);
            hasChanges = true;
        }
        if (qywxStatus != null && !qywxStatus.equals(existingDevice.getQywxStatus())) {
            updateObj.setQywxStatus(qywxStatus);
            hasChanges = true;
        }
        if (wechatStatus != null && !wechatStatus.equals(existingDevice.getWechatStatus())) {
            updateObj.setWechatStatus(wechatStatus);
            hasChanges = true;
        }
        if (dingtalkStatus != null && !dingtalkStatus.equals(existingDevice.getDingtalkStatus())) {
            updateObj.setDingtalkStatus(dingtalkStatus);
            hasChanges = true;
        }
        if (feishuStatus != null && !feishuStatus.equals(existingDevice.getFeishuStatus())) {
            updateObj.setFeishuStatus(feishuStatus);
            hasChanges = true;
        }

        // 只有状态发生变化时才更新数据库
        if (hasChanges) {
            mobileDeviceMapper.updateById(updateObj);
        }
    }

    @Override
    public MobileDeviceDO getMobileDeviceByDeviceId(String deviceId) {
        return mobileDeviceMapper.selectByDeviceId(deviceId);
    }

    @Override
    public MobileDeviceDO createMobileDeviceFromReport(String deviceId, String machineId,
                                                      Integer douyinStatus, Integer xiaohongshuStatus,
                                                      Integer videoStatus, Integer officialAccountStatus,
                                                      Integer qywxStatus, Integer wechatStatus,
                                                      Integer dingtalkStatus, Integer feishuStatus) {
        // 🔥 修复：校验关联的代理器是否存在
        if (machineId != null) {
            validateMachineExists(machineId);
        }

        // 构建设备对象
        MobileDeviceDO newDevice = new MobileDeviceDO();
        newDevice.setDeviceId(deviceId);
        newDevice.setMachineId(machineId);

        // 🔥 修复：设置默认名称（避免数据库字段不能为空的错误）
        newDevice.setName("手机设备-" + deviceId.substring(Math.max(0, deviceId.length() - 8)));

        // 设置默认状态为启用
        newDevice.setStatus(MobileDeviceStatusEnum.ENABLED.getStatus());

        // 设置应用状态
        newDevice.setDouyinStatus(douyinStatus);
        newDevice.setXiaohongshuStatus(xiaohongshuStatus);
        newDevice.setVideoStatus(videoStatus);
        newDevice.setOfficialAccountStatus(officialAccountStatus);
        newDevice.setQywxStatus(qywxStatus);
        newDevice.setWechatStatus(wechatStatus);
        newDevice.setDingtalkStatus(dingtalkStatus);
        newDevice.setFeishuStatus(feishuStatus);

        // 设置最后上报时间
        newDevice.setLastReportTime(LocalDateTime.now());

        // 🔥 手动设置基础字段，因为PhysicalDeleteBaseDO的自动填充可能不工作
        LocalDateTime now = LocalDateTime.now();
        newDevice.setCreateTime(now);
        newDevice.setUpdateTime(now);
        newDevice.setCreator("system");
        newDevice.setUpdater("system");

        // 保存到数据库
        mobileDeviceMapper.insert(newDevice);

        log.info("设备自动创建成功: deviceId={}, machineId={}, id={}",
            deviceId, machineId, newDevice.getId());

        return newDevice;
    }

    @Override
    public int batchUpdateStatusByMachineId(String machineId, Integer status) {
        log.info("批量更新手机设备状态: machineId={}, status={}", machineId, status);

        try {
            // 构建更新条件：根据machineId查找所有设备
            LambdaQueryWrapperX<MobileDeviceDO> queryWrapper = new LambdaQueryWrapperX<MobileDeviceDO>()
                .eq(MobileDeviceDO::getMachineId, machineId);

            // 构建更新对象
            MobileDeviceDO updateObj = new MobileDeviceDO();
            updateObj.setStatus(status);

            // 执行批量更新
            int updateCount = mobileDeviceMapper.update(updateObj, queryWrapper);

            log.info("批量更新手机设备状态完成: machineId={}, status={}, 更新数量={}",
                machineId, status, updateCount);

            return updateCount;

        } catch (Exception e) {
            log.error("批量更新手机设备状态失败: machineId={}, status={}, error={}",
                machineId, status, e.getMessage(), e);
            throw e;
        }
    }

    // ==================== 校验方法 ====================

    private void validateMobileDeviceExists(Long id) {
        if (mobileDeviceMapper.selectById(id) == null) {
            throw exception(ErrorCodeConstants.MOBILE_DEVICE_NOT_EXISTS);
        }
    }

    private MobileDeviceDO validateMobileDeviceExistsAndGet(Long id) {
        MobileDeviceDO device = mobileDeviceMapper.selectById(id);
        if (device == null) {
            throw exception(ErrorCodeConstants.MOBILE_DEVICE_NOT_EXISTS);
        }
        return device;
    }

    private void validateMobileDeviceIdNotExistsForUpdate(Long id, String deviceId) {
        if (mobileDeviceMapper.selectByDeviceId(deviceId) != null && 
            !mobileDeviceMapper.selectByDeviceId(deviceId).getId().equals(id)) {
            throw exception(ErrorCodeConstants.MOBILE_DEVICE_ID_EXISTS);
        }
    }

    private void validateMachineExists(String machineId) {
        if (machinesMapper.selectByMachineId(machineId) == null) {
            throw exception(ErrorCodeConstants.MACHINE_NOT_EXISTS);
        }
    }

    private void validateSimCard1NotExistsForUpdate(Long id, String simCard1) {
        if (simCard1 != null && mobileDeviceMapper.selectBySimCard1(simCard1) != null && 
            !mobileDeviceMapper.selectBySimCard1(simCard1).getId().equals(id)) {
            throw exception(ErrorCodeConstants.SIM_CARD1_EXISTS);
        }
    }

    private void validateSimCard2NotExistsForUpdate(Long id, String simCard2) {
        if (simCard2 != null && mobileDeviceMapper.selectBySimCard2(simCard2) != null && 
            !mobileDeviceMapper.selectBySimCard2(simCard2).getId().equals(id)) {
            throw exception(ErrorCodeConstants.SIM_CARD2_EXISTS);
        }
    }

    private void validateSimCard1NotEqualSimCard2(String simCard1, String simCard2) {
        if (simCard1 != null && simCard2 != null && simCard1.equals(simCard2)) {
            throw exception(ErrorCodeConstants.SIM_CARD1_EQUAL_SIM_CARD2);
        }
    }

    private void validateSimCard1NotExistsInSimCard2ForUpdate(Long id, String simCard1) {
        if (simCard1 != null && mobileDeviceMapper.selectBySimCard2(simCard1) != null && 
            !mobileDeviceMapper.selectBySimCard2(simCard1).getId().equals(id)) {
            throw exception(ErrorCodeConstants.SIM_CARD1_EXISTS);
        }
    }

    private void validateSimCard2NotExistsInSimCard1ForUpdate(Long id, String simCard2) {
        if (simCard2 != null && mobileDeviceMapper.selectBySimCard1(simCard2) != null && 
            !mobileDeviceMapper.selectBySimCard1(simCard2).getId().equals(id)) {
            throw exception(ErrorCodeConstants.SIM_CARD2_EXISTS);
        }
    }

    private String processMachineId(String machineId) {
        return (machineId != null && machineId.trim().isEmpty()) ? null : machineId;
    }

    /**
     * 发送手机设备控制指令到客户端
     *
     * @param device 手机设备信息
     * @param action 操作类型：pause（暂停）或 enable（启用）
     */
    private void sendMobileDeviceControlCommand(MobileDeviceDO device, String action) {
        try {
            // 构建控制指令消息
            Map<String, Object> controlMessage = new HashMap<>();
            controlMessage.put("type", "mobile_device_control");
            controlMessage.put("action", action);
            controlMessage.put("machineId", device.getMachineId());
            controlMessage.put("deviceId", device.getDeviceId());
            controlMessage.put("timestamp", System.currentTimeMillis());

            // 发送WebSocket消息到指定代理器
            webSocketEndpoint.sendObjectToMachine(device.getMachineId(), "mobile_device_control", controlMessage);

            log.info("发送手机设备控制指令成功，代理器ID: {}, 设备标识: {}, 操作: {}",
                    device.getMachineId(), device.getDeviceId(), action);

        } catch (Exception e) {
            log.error("发送手机设备控制指令失败，代理器ID: {}, 设备标识: {}, 操作: {}, 错误: {}",
                    device.getMachineId(), device.getDeviceId(), action, e.getMessage(), e);
        }
    }

    @Override
    public int updateTenantIdByMachineId(String machineId, Long newTenantId) {
        log.info("开始更新代理器关联手机设备的租户ID，代理器ID: {}, 新租户ID: {}", machineId, newTenantId);

        try {
            // 🔥 跨租户查询关联的手机设备
            List<MobileDeviceDO> devices = TenantUtils.executeIgnore(() -> {
                return mobileDeviceMapper.selectByMachineId(machineId);
            });

            if (devices == null || devices.isEmpty()) {
                log.info("代理器 {} 没有关联的手机设备，无需更新", machineId);
                return 0;
            }

            log.info("找到 {} 台关联的手机设备需要更新租户ID", devices.size());

            int updatedCount = 0;
            for (MobileDeviceDO device : devices) {
                Long oldTenantId = device.getTenantId();

                if (!Objects.equals(oldTenantId, newTenantId)) {
                    // 🔥 跨租户更新设备的租户ID
                    TenantUtils.executeIgnore(() -> {
                        MobileDeviceDO updateObj = new MobileDeviceDO();
                        updateObj.setId(device.getId());
                        updateObj.setTenantId(newTenantId);
                        updateObj.setUpdateTime(LocalDateTime.now());
                        updateObj.setUpdater("system-tenant-migration");

                        mobileDeviceMapper.updateById(updateObj);
                        return null;
                    });

                    updatedCount++;
                    log.info("更新手机设备租户ID成功，设备ID: {}, 设备标识: {}, 租户ID: {} -> {}",
                        device.getId(), device.getDeviceId(), oldTenantId, newTenantId);
                } else {
                    log.debug("手机设备 {} 租户ID已经是 {}，无需更新", device.getDeviceId(), newTenantId);
                }
            }

            log.info("代理器关联手机设备租户ID更新完成，代理器ID: {}, 更新设备数量: {}/{}",
                machineId, updatedCount, devices.size());

            return updatedCount;

        } catch (Exception e) {
            log.error("更新代理器关联手机设备租户ID失败，代理器ID: {}, 新租户ID: {}, 错误: {}",
                machineId, newTenantId, e.getMessage(), e);
            throw new RuntimeException("更新手机设备租户ID失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void updateDeviceStatus(Long id, Integer status) {
        MobileDeviceDO updateObj = new MobileDeviceDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        mobileDeviceMapper.updateById(updateObj);
        log.info("🔥 离线检测：更新手机设备状态 id={}, status={}", id, status);
    }

    @Override
    public void updateDeviceStatusByDeviceId(String deviceId, Integer status) {
        MobileDeviceDO device = mobileDeviceMapper.selectByDeviceId(deviceId);
        if (device != null) {
            updateDeviceStatus(device.getId(), status);
        }
    }

    @Override
    public List<MobileDeviceDO> getActiveDevices() {
        // 查询所有未删除的手机设备
        return mobileDeviceMapper.selectList();
    }

}