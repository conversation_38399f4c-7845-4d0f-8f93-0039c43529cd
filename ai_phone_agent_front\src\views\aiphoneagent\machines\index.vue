<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="auto"
    >
      <el-form-item label="代理器编码" prop="machineId">
        <el-input
          v-model="queryParams.machineId"
          clearable
          placeholder="请输入代理器编码"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="代理器名称" prop="name">
        <el-input
          v-model="queryParams.name"
          clearable
          placeholder="请输入代理器名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" clearable placeholder="请选择状态" style="min-width: 120px;">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="操作系统类型" prop="osType">
        <el-select v-model="queryParams.osType" clearable placeholder="请选择操作系统类型" style="min-width: 140px;">
          <el-option label="Windows" value="Windows" />
          <el-option label="Linux" value="Linux" />
        </el-select>
      </el-form-item>
      <el-form-item label="累计运行天数" prop="runDaysMin">
        <el-input-number v-model="queryParams.runDaysMin" :min="0" placeholder="最小天数" style="min-width: 80px;" />
        <span style="margin: 0 8px;">-</span>
        <el-input-number v-model="queryParams.runDaysMax" :min="0" placeholder="最大天数" style="min-width: 80px;" />
      </el-form-item>
      <el-form-item label="注册日期" prop="registerTime">
        <el-date-picker
          v-model="queryParams.registerTime"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <!-- 导出按钮已移除 -->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" stripe style="width: 100%" table-layout="auto">
      <!-- <el-table-column align="center" label="主键" prop="id" width="80" /> -->
      <el-table-column align="center" label="代理器编码" prop="machineId" />
      <el-table-column align="center" label="代理器名称" prop="name" />
      <el-table-column align="center" label="状态" prop="statusName">
        <template #default="scope">
          <el-tag :type="statusTagType(scope.row.status)">{{ scope.row.statusName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作系统类型" prop="osType" />
      <el-table-column align="center" label="累计运行天数" prop="runDays" />
      <el-table-column align="center" label="注册日期" prop="registerTime" />
      <el-table-column align="center" label="操作" min-width="260">
        <template #default="scope">
          <el-button
            v-hasPermi="['aiphoneagent:machines:update']"
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
          >编辑</el-button>
          <el-button
            v-hasPermi="['aiphoneagent:mobile:query']"
            link
            type="info"
            @click="goToMobile(scope.row.machineId)"
          >手机管理</el-button>
          <!-- 🔥 修复：根据状态显示正确的操作按钮 -->
          <!-- 调试信息：显示当前状态值 -->
          <!-- <span style="font-size: 12px; color: #999;">状态值: {{ scope.row.status }}</span> -->

          <el-button
            v-hasPermi="['aiphoneagent:machines:pause']"
            link
            type="warning"
            @click="handlePause(scope.row.id)"
            v-if="scope.row.status === 0"
          >暂停</el-button>
          <el-button
            v-hasPermi="['aiphoneagent:machines:update']"
            link
            type="success"
            @click="handleResume(scope.row.id)"
            v-if="scope.row.status === 3"
          >启动</el-button>
          <!-- 🔥 移除重启功能：前端无法重启客户端，离线和异常状态不显示重启按钮 -->
          <el-button
            v-hasPermi="['aiphoneagent:machines:delete']"
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：仅编辑 -->
  <MachinesForm ref="formRef" @success="getList" />
</template>

<script lang="ts" setup>
// import { dateFormatter } from '@/utils/formatTime'
// import download from '@/utils/download'
import * as MachinesApi from '@/api/aiphoneagent/machines'
import MachinesForm from './MachinesForm.vue'

defineOptions({ name: 'AiphoneagentMachines' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { push } = useRouter() // 路由跳转

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  machineId: undefined,
  name: undefined,
  status: undefined,
  osType: undefined,
  runDaysMin: undefined,
  runDaysMax: undefined,
  registerTime: []
})
const queryFormRef = ref() // 搜索的表单
// const exportLoading = ref(false) // 导出的加载中

const statusOptions = [
  { value: 0, label: '运行中' },
  { value: 1, label: '离线' },
  { value: 2, label: '异常' },
  { value: 3, label: '暂停' }
]

const statusTagType = (status: number) => {
  switch (status) {
    case 0: return 'success'  // 运行中 - 绿色
    case 1: return 'info'     // 离线 - 灰色
    case 2: return 'danger'   // 异常 - 红色
    case 3: return 'warning'  // 暂停 - 橙色
    default: return 'primary'
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await MachinesApi.getMachinesPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 跳转到手机管理页面 */
const goToMobile = (machineId: string) => {
  // 🔥 优化：使用sessionStorage传递过滤信息，避免URL查询参数导致的重复标签页
  console.log('🔥 代理器页面跳转，machineId:', machineId)
  sessionStorage.setItem('mobileFilterMachineId', machineId)
  sessionStorage.setItem('mobileFilterFrom', 'machines')

  // 跳转到手机管理页面，不带任何查询参数，确保URL统一
  push({
    name: 'AiphoneagentMobile'
  })
}

/** 编辑操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}



/** 暂停操作 */
const handlePause = async (id: number) => {
  try {
    await message.confirm('确认要暂停该代理器吗？')
    await MachinesApi.pauseMachines(id)
    message.success('已暂停')
    await getList() // 🔥 修复：使用await确保数据刷新
  } catch (error) {
    console.error('暂停失败:', error)
  }
}

/** 恢复操作 */
const handleResume = async (id: number) => {
  try {
    await message.confirm('确认要启用该代理器吗？')
    await MachinesApi.resumeMachines(id)
    message.success('已启用')
    await getList() // 🔥 修复：使用await确保数据刷新
  } catch (error) {
    console.error('启用失败:', error)
  }
}

/** 删除操作 */
const handleDelete = async (id: number) => {
  try {
    // 🔥 优化：提示用户级联删除的影响
    await message.confirm(
      '确认删除该代理器吗？\n\n⚠️ 注意：删除代理器将同时删除其关联的所有手机设备！',
      '级联删除确认'
    )
    await MachinesApi.deleteMachines(id)
    message.success('删除成功！已同时清理关联的手机设备')
    await getList()
  } catch {}
}

/** 导出操作 */
// const handleExport = async () => {
//   try {
//     await message.exportConfirm()
//     exportLoading.value = true
//     const data = await MachinesApi.exportMachines(queryParams)
//     download.excel(data, '代理器.xls')
//   } catch {
//   } finally {
//     exportLoading.value = false
//   }
// }

/** 初始化 **/
onMounted(() => {
  getList()
})

/** 页面激活时刷新数据 */
onActivated(() => {
  getList()
})
</script> 