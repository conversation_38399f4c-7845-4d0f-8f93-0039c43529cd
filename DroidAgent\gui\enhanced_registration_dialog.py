#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的注册对话框
支持已注册和未注册两种状态的统一处理
"""

import sys
import platform
import re
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                             QLineEdit, QPushButton, QMessageBox, QFormLayout,
                             QTextEdit, QGroupBox, QApplication, QSystemTrayIcon,
                             QMenu, QAction, QWidget)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon, QPixmap


class EnhancedRegistrationDialog(QDialog):
    """增强的注册对话框"""

    def __init__(self, machine_code, is_registered=False, current_tenant_id="", current_domain="", parent=None):
        super().__init__(parent)
        self.machine_code = machine_code
        self.is_registered = is_registered
        self.current_tenant_id = current_tenant_id
        self.current_domain = current_domain
        self.result_action = None
        self.result_tenant_id = ""
        self.result_domain = ""

        # 界面状态控制
        self.edit_mode = not is_registered  # 未注册时默认为编辑模式

        # 🔥 新增：注册管理器（由外部设置）
        self.registration_manager = None

        # 🔥 新增：代理器运行状态
        self.agent_running = False

        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        # 设置窗口属性
        title = "AI手机代理器管理" if self.is_registered else "AI手机代理器注册"
        self.setWindowTitle(title)
        self.setFixedSize(450, 280)
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint)
        
        # 设置窗口居中
        self.center_window()
        
        # 创建主布局
        main_layout = QVBoxLayout()
        
        # 标题
        title_text = "代理器管理" if self.is_registered else "代理器注册"
        title_label = QLabel(title_text)
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        
        # 状态信息
        if self.is_registered:
            status_label = QLabel("当前代理器已注册，您可以启动代理器或修改注册信息")
            status_label.setAlignment(Qt.AlignCenter)
            status_label.setStyleSheet("QLabel { color: #2E8B57; font-size: 12px; margin: 5px; }")
            main_layout.addWidget(status_label)
        else:
            status_label = QLabel("请填写注册信息完成首次注册")
            status_label.setAlignment(Qt.AlignCenter)
            status_label.setStyleSheet("QLabel { color: #FF6347; font-size: 12px; margin: 5px; }")
            main_layout.addWidget(status_label)
        
        # 表单布局
        form_layout = QFormLayout()
        
        # 租户ID输入
        self.tenant_id_edit = QLineEdit()
        if self.current_tenant_id:
            self.tenant_id_edit.setText(self.current_tenant_id)
        self.tenant_id_edit.setPlaceholderText("请输入租户ID,在云管理端左上角查看")
        self.tenant_id_edit.setReadOnly(not self.edit_mode)
        form_layout.addRow("租户ID:", self.tenant_id_edit)

        # 域名输入
        self.domain_edit = QLineEdit()
        if self.current_domain:
            self.domain_edit.setText(self.current_domain)
        self.domain_edit.setPlaceholderText("请输入云管理端域名")
        self.domain_edit.setReadOnly(not self.edit_mode)
        form_layout.addRow("云管理端域名:", self.domain_edit)
        
        main_layout.addLayout(form_layout)
        
        # 按钮布局
        self.create_buttons(main_layout)
        
        # 设置主布局
        self.setLayout(main_layout)

        # 设置输入框样式
        self.update_input_style()

    def update_button_state(self):
        """🔥 新增：更新按钮状态"""
        print(f"更新按钮状态，代理器运行状态: {self.agent_running}")

        try:
            # 🔥 简化方法：直接重新初始化UI
            print("重新初始化UI以更新按钮状态...")

            # 保存当前状态
            current_agent_running = self.agent_running
            current_registration_manager = self.registration_manager

            # 清除当前布局
            if self.layout():
                QWidget().setLayout(self.layout())

            # 重新初始化UI
            self.init_ui()

            # 恢复状态
            self.agent_running = current_agent_running
            self.registration_manager = current_registration_manager

            print("✅ UI重新初始化完成")

            # 🔥 调整窗口大小以适应新内容
            self.adjustSize()
            print("✅ 窗口大小已调整")

        except Exception as e:
            print(f"❌ 更新按钮状态失败: {e}")
            import traceback
            traceback.print_exc()
    
    def create_buttons(self, main_layout):
        """创建按钮"""
        button_layout = QHBoxLayout()
        
        if self.is_registered and not self.edit_mode:
            # 已注册且非编辑模式：根据代理器运行状态显示不同按钮
            if self.agent_running:
                # 代理器已运行：显示"代理器运行中"和"修改注册信息"
                self.status_btn = QPushButton("代理器运行中")
                self.status_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #4CAF50;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        font-size: 14px;
                        border-radius: 4px;
                    }
                """)
                self.status_btn.setEnabled(False)  # 禁用按钮，仅显示状态
                button_layout.addWidget(self.status_btn)
            else:
                # 代理器未运行：显示"启动代理器"
                self.start_btn = QPushButton("启动代理器")
                self.start_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #4CAF50;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        font-size: 14px;
                        border-radius: 4px;
                    }
                    QPushButton:hover {
                        background-color: #45a049;
                    }
                """)
                self.start_btn.clicked.connect(self.on_start)
                button_layout.addWidget(self.start_btn)

            # 始终显示"修改注册信息"按钮
            self.modify_btn = QPushButton("修改注册信息")
            self.modify_btn.setStyleSheet("""
                QPushButton {
                    background-color: #2196F3;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    font-size: 14px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #1976D2;
                }
            """)
            self.modify_btn.clicked.connect(self.on_modify)
            button_layout.addWidget(self.modify_btn)
        else:
            # 编辑模式：显示"注册/确认"和"取消"
            action_text = "确认修改" if self.is_registered else "注册"
            self.register_btn = QPushButton(action_text)
            self.register_btn.setStyleSheet("""
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    font-size: 14px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
            """)
            self.register_btn.clicked.connect(self.on_register)
            button_layout.addWidget(self.register_btn)
            
            self.cancel_btn = QPushButton("取消")
            self.cancel_btn.setStyleSheet("""
                QPushButton {
                    background-color: #f44336;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    font-size: 14px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #d32f2f;
                }
            """)
            self.cancel_btn.clicked.connect(self.on_cancel)
            button_layout.addWidget(self.cancel_btn)
        
        main_layout.addLayout(button_layout)
    
    def update_input_style(self):
        """更新输入框样式"""
        if self.edit_mode:
            # 编辑模式：正常样式
            style = """
                QLineEdit {
                    border: 2px solid #ddd;
                    border-radius: 4px;
                    padding: 5px;
                    font-size: 12px;
                }
                QLineEdit:focus {
                    border-color: #4CAF50;
                }
            """
        else:
            # 只读模式：灰色样式
            style = """
                QLineEdit {
                    border: 2px solid #ddd;
                    border-radius: 4px;
                    padding: 5px;
                    font-size: 12px;
                    background-color: #f5f5f5;
                    color: #666;
                }
            """
        
        self.tenant_id_edit.setStyleSheet(style)
        self.domain_edit.setStyleSheet(style)
    
    def center_window(self):
        """窗口居中"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def validate_input(self):
        """简化验证输入 - 只检查非空"""
        tenant_id = self.tenant_id_edit.text().strip()
        domain = self.domain_edit.text().strip()

        if not tenant_id:
            QMessageBox.warning(self, "输入错误", "请输入租户ID")
            return False

        if not domain:
            QMessageBox.warning(self, "输入错误", "请输入云管理端域名")
            return False

        # 🔥 简化：移除所有格式验证，用户输入什么就是什么
        return True
    

    
    def on_start(self):
        """启动代理器按钮点击"""
        self.result_action = "start"

        # 🔥 启动代理器后，更新状态
        self.agent_running = True

        # 🔥 修复：关闭对话框，让主程序继续执行
        print("启动代理器，关闭对话框")
        self.accept()
    
    def on_modify(self):
        """修改注册信息按钮点击"""
        # 切换到编辑模式
        self.edit_mode = True
        self.tenant_id_edit.setReadOnly(False)
        self.domain_edit.setReadOnly(False)
        self.update_input_style()
        
        # 重新创建按钮
        # 清除现有按钮
        layout = self.layout()
        button_layout = layout.itemAt(layout.count() - 1).layout()
        while button_layout.count():
            child = button_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
        # 创建新按钮
        self.create_buttons(layout)
    
    def on_register(self):
        """注册/确认按钮点击"""
        if not self.validate_input():
            return

        tenant_id = self.tenant_id_edit.text().strip()
        domain = self.domain_edit.text().strip()

        # 🔥 如果有注册管理器，直接执行注册/更新
        if self.registration_manager:
            try:
                if self.is_registered:
                    # 已注册，执行更新
                    print(f"用户选择注册/修改，租户ID: {tenant_id}, 域名: {domain}")
                    success, message = self.registration_manager.update_registration(tenant_id, domain)
                else:
                    # 未注册，执行注册
                    print(f"用户选择首次注册，租户ID: {tenant_id}, 域名: {domain}")
                    success, message = self.registration_manager.register_agent(tenant_id, domain)

                if success:
                    print("注册/更新成功！")
                    # 更新对话框状态
                    self.is_registered = True
                    self.current_tenant_id = tenant_id
                    self.current_domain = domain

                    # 设置结果
                    self.result_action = "register"
                    self.result_tenant_id = tenant_id
                    self.result_domain = domain

                    # 显示成功消息
                    QMessageBox.information(self, "成功", "注册/更新成功！")

                    # 接受对话框
                    self.accept()
                else:
                    print(f"注册/更新失败: {message}")
                    QMessageBox.warning(self, "失败", f"注册/更新失败: {message}")

            except Exception as e:
                print(f"注册/更新异常: {e}")
                QMessageBox.critical(self, "错误", f"注册/更新异常: {e}")
        else:
            # 没有注册管理器，使用原有逻辑
            self.result_action = "register"
            self.result_tenant_id = tenant_id
            self.result_domain = domain
            self.accept()
    
    def on_cancel(self):
        """取消按钮点击"""
        if self.is_registered:
            # 如果是已注册状态的修改，返回到显示模式
            self.edit_mode = False
            self.tenant_id_edit.setReadOnly(True)
            self.domain_edit.setReadOnly(True)
            self.update_input_style()
            
            # 恢复原始值
            self.tenant_id_edit.setText(self.current_tenant_id)
            self.domain_edit.setText(self.current_domain)
            
            # 重新创建按钮
            layout = self.layout()
            button_layout = layout.itemAt(layout.count() - 1).layout()
            while button_layout.count():
                child = button_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
            
            self.create_buttons(layout)
        else:
            # 未注册状态的取消，直接关闭
            self.reject()


def show_enhanced_registration_dialog(machine_code, is_registered=False, current_tenant_id="", current_domain="", parent=None):
    """显示增强的注册对话框"""
    if platform.system().lower() != "windows":
        print("增强注册对话框仅在Windows环境下可用")
        return None
    
    try:
        from PyQt5.QtWidgets import QApplication
        
        # 确保QApplication已初始化
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        dialog = EnhancedRegistrationDialog(machine_code, is_registered, current_tenant_id, current_domain, parent)
        if dialog.exec_() == QDialog.Accepted:
            return (dialog.result_action, dialog.result_tenant_id, dialog.result_domain)
        else:
            return None
    except ImportError:
        print("错误: 需要安装PyQt5库，请运行: pip install PyQt5")
        return None
    except Exception as e:
        print(f"显示增强注册对话框失败: {str(e)}")
        return None


if __name__ == "__main__":
    pass