package cn.iocoder.yudao.module.monitor.service.impl;

import org.springframework.data.redis.core.StringRedisTemplate;
import cn.iocoder.yudao.module.machines.dal.dataobject.MachinesDO;
import cn.iocoder.yudao.module.machines.service.MachinesService;
import cn.iocoder.yudao.module.mobile.dal.dataobject.MobileDeviceDO;
import cn.iocoder.yudao.module.mobile.service.MobileDeviceService;
import cn.iocoder.yudao.module.monitor.config.DeviceMonitorProperties;
import cn.iocoder.yudao.module.monitor.dto.DeviceReportReqDTO;
import cn.iocoder.yudao.module.monitor.dto.DeviceStatusRespDTO;
import cn.iocoder.yudao.module.monitor.dto.DeviceStatusCommandDTO;
import cn.iocoder.yudao.module.monitor.service.DeviceService;
import com.fasterxml.jackson.databind.ObjectMapper;
import cn.iocoder.yudao.module.machines.dal.mysql.MachinesMapper;
import cn.iocoder.yudao.module.mobile.enums.MobileDeviceStatusEnum;
import cn.iocoder.yudao.module.mobile.enums.AppStatusEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 设备监控服务实现类
 *
 * <AUTHOR> Phone Agent
 */
@Slf4j
@Service
public class DeviceServiceImpl implements DeviceService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private DeviceMonitorProperties deviceMonitorProperties;

    @Resource
    private MachinesService machinesService;

    @Resource
    private MobileDeviceService mobileDeviceService;

    @Resource
    private MachinesMapper machinesMapper;

    @Resource
    private ObjectMapper objectMapper;

    @Override
    public void reportDevices(DeviceReportReqDTO request) {
        log.info("接收到设备上报数据: {}", request);
        
        for (DeviceReportReqDTO.DeviceInfo deviceInfo : request.getDevices()) {
            // 存储机器心跳到Redis
            String machineKey = buildMachineKey(deviceInfo.getMachine());
            String deviceKey = buildDeviceKey(deviceInfo.getDevice());
            
            // 存储机器心跳（机器在线状态由TTL机制判断）
            stringRedisTemplate.opsForValue().set(machineKey, "HEARTBEAT", 
                deviceMonitorProperties.getDeviceOnlineTtl(), TimeUnit.SECONDS);
            
            // 存储设备心跳信息（设备在线状态由TTL机制判断）
            Map<String, String> deviceData = new HashMap<>();
            deviceData.put("machineId", deviceInfo.getMachine());
            deviceData.put("lastHeartbeatTime", LocalDateTime.now().toString());
            
            stringRedisTemplate.opsForHash().putAll(deviceKey, deviceData);
            stringRedisTemplate.expire(deviceKey, deviceMonitorProperties.getDeviceOnlineTtl(), TimeUnit.SECONDS);
            
            // 🔥 新增：同步更新数据库中的最后上报时间和应用状态（持久化存储）
            try {
                // 🔥 修复：先检查代理器是否存在，不存在则跳过设备处理
                MachinesDO machine = machinesService.getMachinesByMachineId(deviceInfo.getMachine());
                if (machine == null) {
                    log.warn("代理器不存在，跳过设备处理: machineId={}, deviceId={}",
                        deviceInfo.getMachine(), deviceInfo.getDevice());
                    continue; // 跳过当前设备的处理
                }

                // 更新机器的最后上报时间
                machinesService.updateLastReportTime(deviceInfo.getMachine());
                // 更新移动设备的最后上报时间
                mobileDeviceService.updateLastReportTime(deviceInfo.getDevice());
                log.info("🔍 已更新移动设备最后上报时间: deviceId={}", deviceInfo.getDevice());

                // 🔥 新增：同步在线状态到数据库
                try {
                    // 确保代理器状态为运行中（如果不是暂停状态）
                    syncMachineOnlineStatus(deviceInfo.getMachine());

                    // 确保手机设备状态为在线（如果不是暂停状态）
                    syncDeviceOnlineStatus(deviceInfo.getDevice());

                } catch (Exception e) {
                    log.warn("同步设备在线状态到数据库失败: machine={}, device={}, error={}",
                        deviceInfo.getMachine(), deviceInfo.getDevice(), e.getMessage());
                }

                // 🔥 优化：设备自动发现和应用状态同步
                if (hasAppStatusChanges(deviceInfo)) {
                    // 先确保设备存在，不存在则自动创建
                    ensureDeviceExists(deviceInfo);

                    // 然后同步应用状态
                    mobileDeviceService.updateAppStatuses(
                        deviceInfo.getDevice(),
                        deviceInfo.getDouyinStatus(),
                        deviceInfo.getXiaohongshuStatus(),
                        deviceInfo.getVideoStatus(),
                        deviceInfo.getOfficialAccountStatus(),
                        deviceInfo.getQywxStatus(),
                        deviceInfo.getWechatStatus(),
                        deviceInfo.getDingtalkStatus(),
                        deviceInfo.getFeishuStatus()
                    );
                    log.debug("应用状态已同步到数据库: device={}", deviceInfo.getDevice());
                }

                log.debug("数据库最后上报时间已更新: machine={}, device={}",
                    deviceInfo.getMachine(), deviceInfo.getDevice());
            } catch (Exception e) {
                log.warn("更新数据库最后上报时间失败: machine={}, device={}, error={}",
                    deviceInfo.getMachine(), deviceInfo.getDevice(), e.getMessage());
                // 不影响核心功能，继续处理其他设备
            }
            
            log.debug("设备心跳已记录: machine={}, device={}", 
                deviceInfo.getMachine(), deviceInfo.getDevice());
        }
    }

    @Override
    public List<DeviceStatusRespDTO> getDeviceStatusList() {
        List<MachinesDO> allMachines = machinesService.getMachinesList();
        List<DeviceStatusRespDTO> statusList = new ArrayList<>();
        
        for (MachinesDO machine : allMachines) {
            DeviceStatusRespDTO status = buildDeviceStatus(machine);
            statusList.add(status);
        }
        
        return statusList;
    }

    @Override
    public DeviceStatusRespDTO getDeviceDetails(String machineId) {
        log.info("🔍 获取设备详情，machineId: {}", machineId);
        MachinesDO machine = machinesMapper.selectByMachineId(machineId);
        if (machine == null) {
            throw new IllegalArgumentException("机器不存在: " + machineId);
        }
        log.info("🔍 找到机器: id={}, name={}", machine.getMachineId(), machine.getName());

        // 🔥 临时测试：更新机器的最后上报时间为当前时间
        try {
            machinesService.updateLastReportTime(machineId);
            log.info("🔍 已更新机器最后上报时间: {}", machineId);
        } catch (Exception e) {
            log.warn("更新机器最后上报时间失败: {}", e.getMessage());
        }

        DeviceStatusRespDTO status = buildDeviceStatus(machine);
        List<DeviceStatusRespDTO.MobileDeviceDetail> mobileDevices = getMobileDeviceDetails(machineId);
        status.setMobileDevices(mobileDevices);
        return status;
    }

    /**
     * 构建设备状态响应对象
     */
    private DeviceStatusRespDTO buildDeviceStatus(MachinesDO machine) {
        DeviceStatusRespDTO status = new DeviceStatusRespDTO();
        status.setMachineId(machine.getMachineId());
        status.setMachineName(machine.getName());
        status.setOsType(machine.getOsType());
        status.setRunDays(machine.getRunDays());
        status.setRegisterTime(machine.getRegisterTime());
        status.setLastReportTime(machine.getLastReportTime());
        status.setTenantId(machine.getTenantId());

        // 🔥 优化：机器状态智能判断逻辑
        // 1. 检查机器是否在线（根据Redis中是否存在心跳key且未过期）
        String machineKey = buildMachineKey(machine.getMachineId());
        String machineHeartbeat = stringRedisTemplate.opsForValue().get(machineKey);
        boolean isMachineOnline = machineHeartbeat != null;

        // 2. 如果Redis中没有心跳，检查数据库最后上报时间作为备用判断
        if (!isMachineOnline && machine.getLastReportTime() != null) {
            // 如果最后上报时间在TTL时间内，也认为在线
            LocalDateTime ttlThreshold = LocalDateTime.now().minusSeconds(deviceMonitorProperties.getDeviceOnlineTtl());
            isMachineOnline = machine.getLastReportTime().isAfter(ttlThreshold);
            log.debug("Redis无心跳，使用数据库时间判断: machineId={}, 最后上报={}, TTL阈值={}, 判断在线={}",
                machine.getMachineId(), machine.getLastReportTime(), ttlThreshold, isMachineOnline);
        }

        // 2. 状态判断优先级：业务状态（暂停、异常）> 在线状态
        Integer finalMachineStatus = machine.getStatus(); // 默认使用数据库业务状态
        String finalMachineStatusName = getMachineStatusName(machine.getStatus());

        if (machine.getStatus() == 3) {
            // 业务状态为暂停时，显示暂停状态
            finalMachineStatus = 3;
            finalMachineStatusName = "暂停";
        } else if (machine.getStatus() == 2) {
            // 业务状态为异常时，显示异常状态
            finalMachineStatus = 2;
            finalMachineStatusName = "异常";
        } else if (isMachineOnline) {
            // 业务状态正常且在线时，显示运行中
            finalMachineStatus = 0;
            finalMachineStatusName = "运行中";
        } else {
            // 业务状态正常但离线时，显示离线
            finalMachineStatus = 1;
            finalMachineStatusName = "离线";
        }

        status.setMachineStatus(finalMachineStatus);
        status.setMachineStatusName(finalMachineStatusName);
        // 统计字段
        List<DeviceStatusRespDTO.MobileDeviceDetail> mobileDevices = getMobileDeviceDetails(machine.getMachineId());
        status.setTotalMobileDevices(mobileDevices.size());
        // 🔥 优化：根据新的状态码进行统计
        int onlineCount = (int) mobileDevices.stream().filter(d -> d.getStatus() != null && d.getStatus() == 0).count();
        int offlineCount = (int) mobileDevices.stream().filter(d -> d.getStatus() != null && d.getStatus() == 2).count();
        status.setOnlineMobileDevices(onlineCount);
        status.setOfflineMobileDevices(offlineCount);
        status.setMobileDevices(mobileDevices);
        return status;
    }

    /**
     * 获取指定机器的移动设备详情（优化版 - 从数据库获取权威数据源）
     */
    private List<DeviceStatusRespDTO.MobileDeviceDetail> getMobileDeviceDetails(String machineId) {
        List<DeviceStatusRespDTO.MobileDeviceDetail> mobileDevices = new ArrayList<>();
        List<MobileDeviceDO> deviceList = mobileDeviceService.getMobileDevicesByMachineId(machineId);

        // 🔥 临时测试：更新所有移动设备的最后上报时间
        for (MobileDeviceDO device : deviceList) {
            try {
                mobileDeviceService.updateLastReportTime(device.getDeviceId());
                log.info("🔍 已更新移动设备最后上报时间: {}", device.getDeviceId());
            } catch (Exception e) {
                log.warn("更新移动设备最后上报时间失败: deviceId={}, error={}", device.getDeviceId(), e.getMessage());
            }
        }

        for (MobileDeviceDO device : deviceList) {
            DeviceStatusRespDTO.MobileDeviceDetail detail = new DeviceStatusRespDTO.MobileDeviceDetail();
            detail.setDeviceId(device.getDeviceId());
            detail.setName(device.getName());
            detail.setMachineId(device.getMachineId());
            detail.setOsModel(device.getOsModel());
            detail.setSimCard1(device.getSimCard1());
            detail.setSimCard2(device.getSimCard2());
            detail.setStatus(device.getStatus());
            detail.setStatusName(getMobileStatusName(device.getStatus()));
            detail.setDouyinStatus(device.getDouyinStatus());
            detail.setDouyinStatusName(getAppStatusName(device.getDouyinStatus()));
            detail.setXiaohongshuStatus(device.getXiaohongshuStatus());
            detail.setXiaohongshuStatusName(getAppStatusName(device.getXiaohongshuStatus()));
            detail.setVideoStatus(device.getVideoStatus());
            detail.setVideoStatusName(getAppStatusName(device.getVideoStatus()));
            detail.setOfficialAccountStatus(device.getOfficialAccountStatus());
            detail.setOfficialAccountStatusName(getAppStatusName(device.getOfficialAccountStatus()));
            detail.setQywxStatus(device.getQywxStatus());
            detail.setQywxStatusName(getAppStatusName(device.getQywxStatus()));
            detail.setWechatStatus(device.getWechatStatus());
            detail.setWechatStatusName(getAppStatusName(device.getWechatStatus()));
            detail.setDingtalkStatus(device.getDingtalkStatus());
            detail.setDingtalkStatusName(getAppStatusName(device.getDingtalkStatus()));
            detail.setFeishuStatus(device.getFeishuStatus());
            detail.setFeishuStatusName(getAppStatusName(device.getFeishuStatus()));
            detail.setLastReportTime(device.getLastReportTime());

            // 🔥 优化：智能状态合并逻辑
            // 1. 获取Redis在线状态
            String deviceKey = buildDeviceKey(device.getDeviceId());
            Map<Object, Object> deviceData = stringRedisTemplate.opsForHash().entries(deviceKey);
            boolean isOnline = deviceData != null && !deviceData.isEmpty();

            // 🔥 优化：如果Redis中没有数据，使用TTL时间作为备用判断
            if (!isOnline && device.getLastReportTime() != null) {
                LocalDateTime ttlThreshold = LocalDateTime.now().minusSeconds(deviceMonitorProperties.getDeviceOnlineTtl());
                isOnline = device.getLastReportTime().isAfter(ttlThreshold);
                log.debug("Redis无数据，使用数据库时间判断: deviceId={}, 最后上报={}, TTL阈值={}, 判断在线={}",
                    device.getDeviceId(), device.getLastReportTime(), ttlThreshold, isOnline);
            }

            // 2. 状态判断优先级：业务状态（暂停）> 在线状态
            Integer finalStatus = device.getStatus(); // 默认使用数据库业务状态
            String finalStatusName = getMobileStatusName(device.getStatus());

            if (device.getStatus() == 1) {
                // 业务状态为暂停时，显示暂停状态，不管在线与否
                finalStatus = 1;
                finalStatusName = "暂停";
            } else if (isOnline) {
                // 业务状态为启用且在线时，显示在线
                finalStatus = 0;
                finalStatusName = "在线";
            } else {
                // 业务状态为启用但离线时，显示离线
                finalStatus = 2; // 新增离线状态码
                finalStatusName = "离线";
            }

            detail.setStatus(finalStatus);
            detail.setStatusName(finalStatusName);

            log.info("🔍 设备状态判断: deviceId={}, 数据库状态={}, Redis在线={}, Redis数据={}, 最后上报时间={}, 最终状态={}({})",
                device.getDeviceId(), device.getStatus(), (deviceData != null && !deviceData.isEmpty()),
                deviceData, device.getLastReportTime(), finalStatus, finalStatusName);

            // 3. 应用状态优先使用Redis实时数据，fallback到数据库
            // TODO: 后续可以从Redis获取实时应用状态，当前先使用数据库状态
            mobileDevices.add(detail);
        }
        return mobileDevices;
    }

    /**
     * 构建机器Redis key
     */
    private String buildMachineKey(String machineId) {
        return deviceMonitorProperties.getRedisKeyPrefix() + 
            deviceMonitorProperties.getMachineKeyPrefix() + machineId;
    }

    /**
     * 构建设备Redis key
     */
    private String buildDeviceKey(String deviceId) {
        return deviceMonitorProperties.getRedisKeyPrefix() + 
            deviceMonitorProperties.getMobileKeyPrefix() + deviceId;
    }

    /**
     * 从Redis key中提取设备ID
     */
    private String extractDeviceIdFromKey(String key) {
        String prefix = deviceMonitorProperties.getRedisKeyPrefix() +
            deviceMonitorProperties.getMobileKeyPrefix();
        return key.substring(prefix.length());
    }

    /**
     * 🔥 新增：同步代理器在线状态到数据库
     */
    private void syncMachineOnlineStatus(String machineId) {
        try {
            MachinesDO machine = machinesMapper.selectByMachineId(machineId);
            if (machine != null && machine.getStatus() != null) {
                // 只有当前状态为离线或异常时，才更新为运行中
                // 不影响暂停状态
                if (machine.getStatus() == 1 || machine.getStatus() == 2) { // 离线或异常
                    machinesService.updateMachineStatus(machine.getId(), 0); // 0-运行中
                    log.debug("🟢 代理器在线状态同步: machineId={}, 状态: 离线/异常 -> 运行中", machineId);
                }
            }
        } catch (Exception e) {
            log.warn("同步代理器在线状态失败: machineId={}, error={}", machineId, e.getMessage());
        }
    }

    /**
     * 🔥 新增：同步手机设备在线状态到数据库
     */
    private void syncDeviceOnlineStatus(String deviceId) {
        try {
            MobileDeviceDO device = mobileDeviceService.getMobileDeviceByDeviceId(deviceId);
            if (device != null && device.getStatus() != null) {
                // 只有当前状态为离线时，才更新为启用
                // 不影响暂停状态
                if (device.getStatus() == 2) { // 离线
                    mobileDeviceService.updateDeviceStatus(device.getId(), 0); // 0-启用
                    log.debug("🟢 手机设备在线状态同步: deviceId={}, 状态: 离线 -> 启用", deviceId);
                }
            }
        } catch (Exception e) {
            log.warn("同步手机设备在线状态失败: deviceId={}, error={}", deviceId, e.getMessage());
        }
    }

    private String getMachineStatusName(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "运行中";
            case 1: return "离线";
            case 2: return "异常";
            case 3: return "暂停";
            default: return "未知";
        }
    }
    private String getMobileStatusName(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "启用";
            case 1: return "暂停";
            default: return "未知";
        }
    }
    private String getAppStatusName(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "未安装";
            case 1: return "异常";
            case 2: return "运行中";
            default: return "未知";
        }
    }

    /**
     * 检查设备信息中是否包含应用状态变化
     */
    private boolean hasAppStatusChanges(DeviceReportReqDTO.DeviceInfo deviceInfo) {
        return deviceInfo.getDouyinStatus() != null ||
               deviceInfo.getXiaohongshuStatus() != null ||
               deviceInfo.getVideoStatus() != null ||
               deviceInfo.getOfficialAccountStatus() != null ||
               deviceInfo.getQywxStatus() != null ||
               deviceInfo.getWechatStatus() != null ||
               deviceInfo.getDingtalkStatus() != null ||
               deviceInfo.getFeishuStatus() != null;
    }

    @Override
    public void sendDeviceStatusCommand(String machineId, String action, String deviceId, Integer status) {
        try {
            DeviceStatusCommandDTO command;

            // 根据操作类型创建相应的指令
            switch (action) {
                case "evt_device_pause":
                    command = DeviceStatusCommandDTO.createDevicePauseCommand(deviceId, machineId);
                    break;
                case "evt_device_enable":
                    command = DeviceStatusCommandDTO.createDeviceEnableCommand(deviceId, machineId);
                    break;
                case "evt_machine_pause":
                    command = DeviceStatusCommandDTO.createMachinePauseCommand(machineId);
                    break;
                case "evt_machine_enable":
                    command = DeviceStatusCommandDTO.createMachineEnableCommand(machineId);
                    break;
                default:
                    log.warn("未知的设备状态操作: {}", action);
                    return;
            }

            // 将指令序列化为JSON
            String commandJson = objectMapper.writeValueAsString(command);

            // 🔥 TODO: 这里需要通过WebSocket发送指令给客户端
            // 由于模块依赖问题，暂时记录日志，后续可以通过事件机制或其他方式实现
            log.info("设备状态指令已生成: machineId={}, action={}, deviceId={}, command={}",
                machineId, action, deviceId, commandJson);

            // 可以考虑：
            // 1. 发布Spring事件，由channel模块监听并发送WebSocket消息
            // 2. 通过Redis发布订阅机制
            // 3. 直接调用WebSocket服务（需要解决模块依赖问题）

        } catch (Exception e) {
            log.error("发送设备状态指令失败: machineId={}, action={}, deviceId={}",
                machineId, action, deviceId, e);
        }
    }

    /**
     * 🔥 新增：确保设备存在，不存在则自动创建
     */
    private void ensureDeviceExists(DeviceReportReqDTO.DeviceInfo deviceInfo) {
        try {
            // 检查设备是否已存在
            MobileDeviceDO existingDevice = mobileDeviceService.getMobileDeviceByDeviceId(deviceInfo.getDevice());

            if (existingDevice == null) {
                log.info("设备不存在，开始自动创建: deviceId={}, machineId={}",
                    deviceInfo.getDevice(), deviceInfo.getMachine());

                // 自动创建设备记录
                createDeviceFromReport(deviceInfo);

                log.info("设备自动创建成功: deviceId={}", deviceInfo.getDevice());
            } else {
                log.debug("设备已存在: deviceId={}", deviceInfo.getDevice());
            }
        } catch (Exception e) {
            log.error("确保设备存在时发生错误: deviceId={}, error={}",
                deviceInfo.getDevice(), e.getMessage(), e);
            // 不抛出异常，避免影响其他设备的处理
        }
    }

    /**
     * 🔥 新增：从上报信息创建设备记录
     */
    private void createDeviceFromReport(DeviceReportReqDTO.DeviceInfo deviceInfo) {
        try {
            // 调用MobileDeviceService的创建方法
            MobileDeviceDO newDevice = mobileDeviceService.createMobileDeviceFromReport(
                deviceInfo.getDevice(),
                deviceInfo.getMachine(),
                deviceInfo.getDouyinStatus(),
                deviceInfo.getXiaohongshuStatus(),
                deviceInfo.getVideoStatus(),
                deviceInfo.getOfficialAccountStatus(),
                deviceInfo.getQywxStatus(),
                deviceInfo.getWechatStatus(),
                deviceInfo.getDingtalkStatus(),
                deviceInfo.getFeishuStatus()
            );

            log.info("设备自动创建成功: deviceId={}, machineId={}, 应用状态数量={}",
                deviceInfo.getDevice(),
                deviceInfo.getMachine(),
                countAppStatuses(deviceInfo));

        } catch (Exception e) {
            log.error("创建设备记录失败: deviceId={}, error={}",
                deviceInfo.getDevice(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 统计设备信息中的应用状态数量
     */
    private int countAppStatuses(DeviceReportReqDTO.DeviceInfo deviceInfo) {
        int count = 0;
        if (deviceInfo.getDouyinStatus() != null) count++;
        if (deviceInfo.getXiaohongshuStatus() != null) count++;
        if (deviceInfo.getVideoStatus() != null) count++;
        if (deviceInfo.getOfficialAccountStatus() != null) count++;
        if (deviceInfo.getQywxStatus() != null) count++;
        if (deviceInfo.getWechatStatus() != null) count++;
        if (deviceInfo.getDingtalkStatus() != null) count++;
        if (deviceInfo.getFeishuStatus() != null) count++;
        return count;
    }
}