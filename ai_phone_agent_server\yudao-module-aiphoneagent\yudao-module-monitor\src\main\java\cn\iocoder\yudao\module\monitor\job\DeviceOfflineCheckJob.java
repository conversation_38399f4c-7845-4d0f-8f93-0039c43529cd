package cn.iocoder.yudao.module.monitor.job;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.machines.dal.dataobject.MachinesDO;
import cn.iocoder.yudao.module.machines.service.MachinesService;
import cn.iocoder.yudao.module.mobile.dal.dataobject.MobileDeviceDO;
import cn.iocoder.yudao.module.mobile.service.MobileDeviceService;
import cn.iocoder.yudao.module.monitor.config.DeviceMonitorProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 设备离线检测定时任务
 * 
 * 功能：
 * 1. 定期检查Redis中的设备心跳状态
 * 2. 将离线的设备状态同步到machines和mobile模块的数据库
 * 3. 确保前端查询时能看到正确的离线状态
 *
 * <AUTHOR> Phone Agent
 */
@Component
@Slf4j
public class DeviceOfflineCheckJob {

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    
    @Resource
    private DeviceMonitorProperties deviceMonitorProperties;
    
    @Resource
    private MachinesService machinesService;
    
    @Resource
    private MobileDeviceService mobileDeviceService;

    /**
     * 离线检测定时任务
     * 每分钟执行一次，检查设备离线状态并同步数据库
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void checkAndUpdateOfflineDevices() {
        log.info("🔍 开始执行设备离线检测定时任务");

        try {
            // 🔥 修复：使用租户上下文执行任务
            TenantUtils.execute(1L, () -> {
                // 1. 检查代理器离线状态
                int offlineMachineCount = checkMachineOfflineStatus();

                // 2. 检查手机设备离线状态
                int offlineDeviceCount = checkMobileDeviceOfflineStatus();

                log.info("✅ 设备离线检测完成，离线代理器: {}, 离线手机设备: {}",
                    offlineMachineCount, offlineDeviceCount);
                return null;
            });

        } catch (Exception e) {
            log.error("❌ 设备离线检测任务执行失败", e);
        }
    }

    /**
     * 检查代理器离线状态
     * @return 检测到的离线代理器数量
     */
    private int checkMachineOfflineStatus() {
        int offlineCount = 0;
        
        try {
            // 查询所有活跃的代理器
            List<MachinesDO> machines = machinesService.getActiveMachines();
            log.debug("检查代理器离线状态，总数: {}", machines.size());
            
            for (MachinesDO machine : machines) {
                try {
                    // 构建Redis key
                    String machineKey = buildMachineKey(machine.getMachineId());
                    
                    // 检查Redis中是否有心跳
                    String heartbeat = stringRedisTemplate.opsForValue().get(machineKey);
                    boolean isOnline = heartbeat != null;
                    
                    // 判断是否需要更新为离线状态
                    boolean shouldBeOffline = !isOnline && 
                        machine.getStatus() != null && 
                        machine.getStatus() != 1 && // 不是离线状态
                        machine.getStatus() != 3;   // 不是暂停状态
                    
                    if (shouldBeOffline) {
                        // 使用数据库时间作为备用判断
                        if (machine.getLastReportTime() != null) {
                            LocalDateTime ttlThreshold = LocalDateTime.now()
                                .minusSeconds(deviceMonitorProperties.getDeviceOnlineTtl());
                            boolean isRecentlyReported = machine.getLastReportTime().isAfter(ttlThreshold);
                            
                            if (isRecentlyReported) {
                                // 最近有上报，不认为是离线
                                continue;
                            }
                        }
                        
                        // 更新为离线状态
                        machinesService.updateMachineStatus(machine.getId(), 1); // 1-离线
                        offlineCount++;
                        
                        log.info("🔴 代理器离线状态同步: machineId={}, name={}", 
                            machine.getMachineId(), machine.getName());
                    }
                    
                } catch (Exception e) {
                    log.warn("检查代理器离线状态失败: machineId={}, error={}", 
                        machine.getMachineId(), e.getMessage());
                }
            }
            
        } catch (Exception e) {
            log.error("批量检查代理器离线状态失败", e);
        }
        
        return offlineCount;
    }

    /**
     * 检查手机设备离线状态
     * @return 检测到的离线设备数量
     */
    private int checkMobileDeviceOfflineStatus() {
        int offlineCount = 0;
        
        try {
            // 查询所有活跃的手机设备
            List<MobileDeviceDO> devices = mobileDeviceService.getActiveDevices();
            log.debug("检查手机设备离线状态，总数: {}", devices.size());
            
            for (MobileDeviceDO device : devices) {
                try {
                    // 构建Redis key
                    String deviceKey = buildDeviceKey(device.getDeviceId());
                    
                    // 检查Redis中是否有心跳数据
                    Map<Object, Object> deviceData = stringRedisTemplate.opsForHash().entries(deviceKey);
                    boolean isOnline = deviceData != null && !deviceData.isEmpty();
                    
                    // 判断是否需要更新为离线状态
                    boolean shouldBeOffline = !isOnline && 
                        device.getStatus() != null && 
                        device.getStatus() != 2 && // 不是离线状态
                        device.getStatus() != 1;   // 不是暂停状态
                    
                    if (shouldBeOffline) {
                        // 使用数据库时间作为备用判断
                        if (device.getLastReportTime() != null) {
                            LocalDateTime ttlThreshold = LocalDateTime.now()
                                .minusSeconds(deviceMonitorProperties.getDeviceOnlineTtl());
                            boolean isRecentlyReported = device.getLastReportTime().isAfter(ttlThreshold);
                            
                            if (isRecentlyReported) {
                                // 最近有上报，不认为是离线
                                continue;
                            }
                        }
                        
                        // 更新为离线状态
                        mobileDeviceService.updateDeviceStatus(device.getId(), 2); // 2-离线
                        offlineCount++;
                        
                        log.info("🔴 手机设备离线状态同步: deviceId={}, name={}", 
                            device.getDeviceId(), device.getName());
                    }
                    
                } catch (Exception e) {
                    log.warn("检查手机设备离线状态失败: deviceId={}, error={}", 
                        device.getDeviceId(), e.getMessage());
                }
            }
            
        } catch (Exception e) {
            log.error("批量检查手机设备离线状态失败", e);
        }
        
        return offlineCount;
    }

    /**
     * 构建代理器Redis key
     */
    private String buildMachineKey(String machineId) {
        return deviceMonitorProperties.getRedisKeyPrefix() + 
               deviceMonitorProperties.getMachineKeyPrefix() + machineId;
    }

    /**
     * 构建设备Redis key
     */
    private String buildDeviceKey(String deviceId) {
        return deviceMonitorProperties.getRedisKeyPrefix() + 
               deviceMonitorProperties.getMobileKeyPrefix() + deviceId;
    }
}
