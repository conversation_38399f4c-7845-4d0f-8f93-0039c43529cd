<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3bd0a812-c473-4db7-9f34-4ff4db325111" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/yudao-server/src/main/resources/application-local.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/src/main/resources/application-local.yaml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2zibOxPBzBvYL5Vyj32SL9gwIru" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
    <option name="sortByType" value="true" />
    <option name="sortKey" value="BY_TYPE" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.yudao [clean].executor": "Run",
    "Maven.yudao [compile].executor": "Run",
    "Maven.yudao [verify].executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "zhuce",
    "last_opened_file_path": "F:/pythonproject/AIphone/ai_phone_agent_server",
    "settings.editor.selected.configurable": "com.itangcent.idea.plugin.configurable.AccountConfigurable.apifox",
    "应用程序.YudaoServerApplication.executor": "Run"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="F:\pythonproject\AIphone\ai_phone_agent_server" />
      <recent name="F:\pythonproject\AIphone\ai_phone_agent_server\sql\mysql" />
      <recent name="F:\pythonproject\AIphone\ai_phone_agent_server\yudao-module-aiphoneagent\yudao-module-open\test" />
      <recent name="F:\pythonproject\AIphone\ai_phone_agent_server\yudao-module-aiphoneagent\yudao-module-channel\sql" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="F:\pythonproject\AIphone\ai_phone_agent_server\sql\mysql" />
    </key>
  </component>
  <component name="RunManager">
    <configuration default="true" type="Application" factoryName="Application">
      <shortenClasspath name="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="YudaoServerApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="cn.iocoder.yudao.server.YudaoServerApplication" />
      <module name="yudao-server" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="cn.iocoder.yudao.server.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="ai_phone_agent_server" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="ai_phone_agent_server" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.YudaoServerApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="3bd0a812-c473-4db7-9f34-4ff4db325111" name="更改" comment="" />
      <created>1752216812802</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752216812802</updated>
    </task>
    <servers />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="java-exception">
          <properties class="cn.iocoder.yudao.framework.common.exception.ServiceException" package="cn.iocoder.yudao.framework.common.exception" />
          <option name="timeStamp" value="1" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>