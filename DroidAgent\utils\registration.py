#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
注册管理器
"""

import json
import os
import requests
from datetime import datetime
try:
    from .machine_code import get_machine_code
except ImportError:
    from machine_code import get_machine_code

try:
    from .loggable import LoggableMixin
except ImportError:
    from loggable import LoggableMixin

try:
    from .os_utils import get_os_type
except ImportError:
    from os_utils import get_os_type


class RegistrationManager(LoggableMixin):
    """注册管理工具类 V2 - 支持查询、注册、更新分离"""
    
    def __init__(self):
        super().__init__()
        self.config_file = "config/agent_config.json"
        self.api_host = "http://localhost:48080"  # 默认API地址
        self.ensure_config_dir()
        self.load_config()
    
    def ensure_config_dir(self):
        """确保配置目录存在"""
        config_dir = os.path.dirname(self.config_file)
        if not os.path.exists(config_dir):
            os.makedirs(config_dir)
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = {
                    "machineId": "",
                    "tenantId": "",
                    "cloudDomain": "",
                    "osType": get_os_type(),  # 添加操作系统类型
                    "registered": False,
                    "register_time": "",
                    "api_host": self.api_host
                }
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {str(e)}")
            self.config = {
                "machineId": "",
                "tenantId": "",
                "cloudDomain": "",
                "osType": get_os_type(),  # 添加操作系统类型
                "registered": False,
                "register_time": "",
                "api_host": self.api_host
            }
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {str(e)}")
            return False

    def _generate_api_host(self, cloud_domain):
        """🔥 简化：根据云管理端域名生成API主机地址 - 用户输入什么就是什么"""
        try:
            # 🔥 简化逻辑：用户输入什么就是什么，不做复杂判断
            if cloud_domain.startswith('http://') or cloud_domain.startswith('https://'):
                # 如果用户已经包含协议，直接使用
                if ':48080' not in cloud_domain and not cloud_domain.endswith(':48080'):
                    # 如果没有端口48080，添加端口
                    api_host = f"{cloud_domain}:48080"
                else:
                    api_host = cloud_domain
            else:
                # 如果用户没有包含协议，默认添加http://和端口48080
                if ':' in cloud_domain:
                    # 用户已经指定了端口
                    api_host = f"http://{cloud_domain}"
                else:
                    # 用户没有指定端口，添加默认端口48080
                    api_host = f"http://{cloud_domain}:48080"

            self.logger.info(f"域名转换: {cloud_domain} -> {api_host}")
            return api_host

        except Exception as e:
            self.logger.error(f"生成API主机地址失败: {e}")
            # 回退到用户输入的原始值
            return cloud_domain if cloud_domain else "http://localhost:48080"


    
    def get_machine_code(self):
        """🔥 安全获取机器码 - 始终基于硬件生成，不信任配置文件"""
        # 🔥 关键修改：始终从硬件生成机器码，不读取配置文件
        hardware_machine_code = get_machine_code()
        if not hardware_machine_code:
            self.logger.error("无法从硬件生成机器码")
            return None

        # 🔥 验证配置文件中的机器码是否与硬件匹配
        config_machine_code = self.config.get("machineId")
        if config_machine_code and config_machine_code != hardware_machine_code:
            self.logger.warning(f"检测到配置文件中的机器码与硬件不匹配！")
            self.logger.warning(f"配置文件机器码: {config_machine_code}")
            self.logger.warning(f"硬件生成机器码: {hardware_machine_code}")
            self.logger.warning("将使用硬件生成的机器码，并更新配置文件")

            # 🔥 重置注册状态，因为机器码不匹配
            self.config["registered"] = False
            self.config["register_time"] = ""

        # 🔥 始终使用硬件生成的机器码，并更新配置文件
        self.config["machineId"] = hardware_machine_code
        self.config["osType"] = get_os_type()  # 同时更新操作系统类型
        self.save_config()

        return hardware_machine_code
    
    def query_machines_status(self, machineId):
        """查询代理器状态"""
        try:
            url = f"{self.api_host}/admin-api/machines/status/{machineId}"
            
            self.logger.info(f"查询代理器状态: {url}")
            response = requests.get(url, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                self.logger.info(f"完整接口响应: {result}")
                if result.get("code") == 0:
                    data = result.get("data", {})
                    self.logger.info("查询成功")
                    return True, data
                else:
                    error_msg = result.get("msg", "查询失败")
                    self.logger.error(f"查询失败: {error_msg}")
                    return False, error_msg
            else:
                error_msg = f"HTTP错误: {response.status_code}"
                self.logger.error(error_msg)
                return False, error_msg
                
        except requests.exceptions.Timeout:
            error_msg = "请求超时"
            self.logger.error(error_msg)
            return False, error_msg
        except requests.exceptions.ConnectionError:
            error_msg = "连接服务器失败"
            self.logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"查询失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def register_machines(self, machineId, tenantId, cloudDomain):
        """注册代理器"""
        try:
            url = f"{self.api_host}/admin-api/machines/report"
            data = {
                "machineId": machineId,
                "tenantId": tenantId,
                "cloudDomain": cloudDomain,
                "osType": get_os_type()  # 添加操作系统类型
            }

            response = requests.post(url, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    response_data = result.get("data", {})
                    if response_data.get("success"):
                        self.logger.info("代理器注册成功")
                        return True, response_data.get("message", "注册成功")
                    else:
                        error_msg = response_data.get("message", "注册失败")
                        self.logger.error(f"注册失败: {error_msg}")
                        return False, error_msg
                else:
                    error_msg = result.get("msg", "注册失败")
                    self.logger.error(f"注册失败: {error_msg}")
                    return False, error_msg
            else:
                error_msg = f"HTTP错误: {response.status_code}"
                self.logger.error(error_msg)
                return False, error_msg
                
        except requests.exceptions.Timeout:
            error_msg = "请求超时"
            self.logger.error(error_msg)
            return False, error_msg
        except requests.exceptions.ConnectionError:
            error_msg = "连接服务器失败"
            self.logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"注册失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def update_machines(self, machineId, tenantId, cloudDomain):
        """更新代理器信息"""
        try:
            url = f"{self.api_host}/admin-api/machines/info"
            data = {
                "machineId": machineId,
                "tenantId": tenantId,
                "cloudDomain": cloudDomain,
                "osType": get_os_type()  # 添加操作系统类型
            }
            
            self.logger.info(f"更新代理器信息: {url}")
            response = requests.put(url, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    response_data = result.get("data", {})
                    if response_data.get("success"):
                        self.logger.info("代理器信息更新成功")
                        return True, response_data.get("message", "更新成功")
                    else:
                        error_msg = response_data.get("message", "更新失败")
                        self.logger.error(f"更新失败: {error_msg}")
                        return False, error_msg
                else:
                    error_msg = result.get("msg", "更新失败")
                    self.logger.error(f"更新失败: {error_msg}")
                    return False, error_msg
            else:
                error_msg = f"HTTP错误: {response.status_code}"
                self.logger.error(error_msg)
                return False, error_msg
                
        except requests.exceptions.Timeout:
            error_msg = "请求超时"
            self.logger.error(error_msg)
            return False, error_msg
        except requests.exceptions.ConnectionError:
            error_msg = "连接服务器失败"
            self.logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"更新失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def validate_registration_integrity(self):
        """🔥 新增：验证注册状态的完整性"""
        try:
            # 1. 获取真实的硬件机器码
            hardware_machine_code = get_machine_code()
            if not hardware_machine_code:
                self.logger.error("无法获取硬件机器码")
                return False, "无法获取硬件机器码"

            # 2. 检查配置文件完整性
            config_machine_code = self.config.get("machineId")
            config_registered = self.config.get("registered", False)

            # 3. 验证机器码一致性
            if config_machine_code != hardware_machine_code:
                self.logger.warning("机器码不匹配，可能是配置文件被篡改或系统环境变更")
                self.logger.info(f"配置文件机器码: {config_machine_code}")
                self.logger.info(f"硬件生成机器码: {hardware_machine_code}")

                # 重置注册状态
                self.config["machineId"] = hardware_machine_code
                self.config["registered"] = False
                self.config["register_time"] = ""
                self.config["osType"] = get_os_type()
                self.save_config()

                return False, "机器码不匹配，已重置注册状态"

            # 4. 如果本地显示已注册，验证服务器状态
            if config_registered:
                self.logger.info("本地配置显示已注册，验证服务器状态...")
                success, server_data = self.query_machines_status(hardware_machine_code)

                if not success:
                    self.logger.warning("服务器查询失败，可能需要重新注册")
                    return False, "服务器查询失败"

                if not server_data.get("registered", False):
                    self.logger.warning("服务器显示未注册，本地状态不一致")
                    # 同步服务器状态到本地
                    self.config["registered"] = False
                    self.config["register_time"] = ""
                    self.save_config()
                    return False, "服务器显示未注册，已同步本地状态"

                # 检查注册信息是否一致（支持Linux配置文件修改场景）
                server_tenant_id = str(server_data.get("tenantId", ""))
                server_cloud_domain = server_data.get("cloudDomain", "")
                local_tenant_id = str(self.config.get("tenantId", ""))
                local_cloud_domain = self.config.get("cloudDomain", "")

                if (server_tenant_id != local_tenant_id or
                    server_cloud_domain != local_cloud_domain):
                    self.logger.info("注册信息不一致，需要进一步检查")
                    self.logger.info(f"服务器: 租户ID='{server_tenant_id}', 域名='{server_cloud_domain}'")
                    self.logger.info(f"本地: 租户ID='{local_tenant_id}', 域名='{local_cloud_domain}'")
                    return False, "注册信息不一致"

                self.logger.info("注册状态验证通过")
                return True, "注册状态正常"
            else:
                self.logger.info("本地配置显示未注册")
                return False, "未注册"

        except Exception as e:
            self.logger.error(f"验证注册完整性失败: {e}")
            return False, f"验证失败: {e}"

    def check_and_sync_registration(self):
        """🔥 重构：检查并同步注册状态 - 增强安全性"""
        try:
            # 1. 首先验证注册状态完整性
            self.logger.info("验证注册状态完整性...")
            integrity_valid, integrity_message = self.validate_registration_integrity()

            if integrity_valid:
                self.logger.info("注册状态验证通过，无需重新注册")
                return "OK", "已注册"

            self.logger.info(f"注册状态验证失败: {integrity_message}")

            # 2. 获取真实的硬件机器码
            machine_code = self.get_machine_code()
            if not machine_code:
                self.logger.error("无法获取机器码")
                return False, "无法获取机器码"

            # 3. 查询服务器状态
            self.logger.info("查询服务器注册状态...")
            success, server_data = self.query_machines_status(machine_code)

            if not success:
                self.logger.warning("无法查询服务器状态，可能需要注册")
                return False, "无法查询服务器状态"
            
            # 2. 分析服务器状态
            server_registered = server_data.get("registered", False)
            server_tenant_id = str(server_data.get("tenantId", ""))  # 转换为字符串
            server_cloud_domain = server_data.get("cloudDomain", "")
            
            # 3. 检查本地状态
            local_registered = self.config.get("registered", False)
            local_tenant_id = str(self.config.get("tenantId", ""))  # 转换为字符串
            local_cloud_domain = self.config.get("cloudDomain", "")
            
            self.logger.info(f"服务器状态: 已注册={server_registered}, 租户ID={server_tenant_id} (类型: {type(server_data.get('tenantId'))})")
            self.logger.info(f"本地状态: 已注册={local_registered}, 租户ID={local_tenant_id} (类型: {type(self.config.get('tenantId'))})")
            self.logger.info(f"服务器云域名: '{server_cloud_domain}' (类型: {type(server_cloud_domain)})")
            self.logger.info(f"本地云域名: '{local_cloud_domain}' (类型: {type(local_cloud_domain)})")
            
            # 4. 判断需要执行的操作
            if not server_registered:
                # 服务器未注册，需要注册
                self.logger.info("服务器未注册，需要注册")
                return "NEED_REGISTER", "需要注册"
            
            elif not local_registered:
                # 服务器已注册，本地未注册，同步本地状态
                self.logger.info("服务器已注册，同步本地状态")
                self.config.update({
                    "tenantId": server_tenant_id,
                    "cloudDomain": server_cloud_domain,
                    "registered": True,
                    "register_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                })
                self.save_config()
                return "SYNCED", "已同步本地状态"
            
            elif (server_tenant_id != local_tenant_id or
                  server_cloud_domain != local_cloud_domain):
                # 信息不一致，需要更新服务器
                self.logger.info(f"信息不一致，需要更新服务器")
                self.logger.info(f"租户ID比较: '{server_tenant_id}' != '{local_tenant_id}' -> {server_tenant_id != local_tenant_id}")
                self.logger.info(f"云域名比较: '{server_cloud_domain}' != '{local_cloud_domain}' -> {server_cloud_domain != local_cloud_domain}")

                # 详细分析哪个字段不匹配
                if server_tenant_id != local_tenant_id:
                    self.logger.info(f"  租户ID不匹配: 服务器='{server_tenant_id}', 本地='{local_tenant_id}'")
                if server_cloud_domain != local_cloud_domain:
                    self.logger.info(f"  云域名不匹配: 服务器='{server_cloud_domain}', 本地='{local_cloud_domain}'")

                # 🔥 修复：返回NEED_UPDATE，让调用方使用本地配置更新服务器
                self.logger.info("本地配置与服务器不一致，需要更新服务器")
                return "NEED_UPDATE", "本地配置已修改，需要更新服务器"
            
            else:
                # 状态一致，无需操作
                self.logger.info("状态一致，无需操作")
                return "OK", "状态正常"
                
        except Exception as e:
            self.logger.error(f"检查注册状态失败: {str(e)}")
            return False, f"检查失败: {str(e)}"
    
    def register_agent(self, tenantId, cloudDomain):
        """注册代理器"""
        try:
            machine_code = self.get_machine_code()
            if not machine_code:
                self.logger.error("无法获取机器码")
                return False, "无法获取机器码"

            self.logger.info(f"开始注册代理器: tenantId={tenantId}, cloudDomain={cloudDomain}")

            # 🔥 新增：根据cloudDomain生成api_host
            api_host = self._generate_api_host(cloudDomain)
            self.logger.info(f"根据云域名生成API主机: {cloudDomain} -> {api_host}")

            # 🔥 更新api_host到实例变量，用于后续API调用
            self.api_host = api_host

            # 调用注册接口
            success, message = self.register_machines(machine_code, tenantId, cloudDomain)
            if not success:
                self.logger.error(f"注册失败: {message}")
                return False, f"注册失败: {message}"

            # 注册成功，更新本地配置
            self.config.update({
                "tenantId": tenantId,
                "cloudDomain": cloudDomain,
                "osType": get_os_type(),  # 添加操作系统类型
                "registered": True,
                "register_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "api_host": api_host  # 🔥 新增：同步api_host到配置文件
            })

            # 保存配置
            if not self.save_config():
                self.logger.error("保存本地配置文件失败")
                return False, "保存本地配置文件失败"
            
            self.logger.info("代理器注册成功 - 服务器和本地配置已同步")
            return True, "注册成功 - 服务器和本地配置已同步"
            
        except Exception as e:
            self.logger.error(f"注册失败: {str(e)}")
            return False, f"注册失败: {str(e)}"
    
    def update_registration(self, tenantId, cloudDomain):
        """更新注册信息"""
        try:
            machine_code = self.get_machine_code()
            if not machine_code:
                return False, "无法获取机器码"

            self.logger.info(f"开始更新注册信息: tenantId={tenantId}, cloudDomain={cloudDomain}")

            # 🔥 新增：根据cloudDomain生成api_host
            api_host = self._generate_api_host(cloudDomain)
            self.logger.info(f"根据云域名生成API主机: {cloudDomain} -> {api_host}")

            # 🔥 更新api_host到实例变量，用于后续API调用
            self.api_host = api_host

            # 调用更新接口
            success, message = self.update_machines(machine_code, tenantId, cloudDomain)
            if not success:
                self.logger.error(f"更新失败: {message}")
                return False, f"更新失败: {message}"

            # 更新成功，更新本地配置
            self.config.update({
                "tenantId": tenantId,
                "cloudDomain": cloudDomain,
                "osType": get_os_type(),  # 添加操作系统类型
                "registered": True,
                "register_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "api_host": api_host  # 🔥 新增：同步api_host到配置文件
            })

            # 保存配置
            if not self.save_config():
                self.logger.error("保存本地配置文件失败")
                return False, "保存本地配置文件失败"
            
            self.logger.info("注册信息更新成功 - 服务器和本地配置已同步")
            return True, "更新成功 - 服务器和本地配置已同步"
            
        except Exception as e:
            self.logger.error(f"更新注册信息失败: {str(e)}")
            return False, f"更新失败: {str(e)}"
    
    def get_registration_info(self):
        """获取注册信息"""
        return {
            "machineId": self.config.get("machineId", ""),
            "tenantId": self.config.get("tenantId", ""),
            "cloudDomain": self.config.get("cloudDomain", ""),
            "osType": self.config.get("osType", get_os_type()),  # 添加操作系统类型
            "registered": self.config.get("registered", False),
            "register_time": self.config.get("register_time", "")
        }
    
    def reset_registration(self):
        """重置注册信息"""
        self.config.update({
            "tenantId": "",
            "cloudDomain": "",
            "registered": False,
            "register_time": ""
        })
        return self.save_config()
    
    def sync_to_server_data(self, server_data):
        """同步本地配置到服务器数据"""
        try:
            server_tenant_id = str(server_data.get("tenantId", ""))
            server_cloud_domain = server_data.get("cloudDomain", "")
            
            self.logger.info(f"同步本地配置到服务器数据")
            self.logger.info(f"服务器租户ID: {server_tenant_id}")
            self.logger.info(f"服务器云域名: {server_cloud_domain}")
            
            # 更新本地配置
            self.config.update({
                "tenantId": server_tenant_id,
                "cloudDomain": server_cloud_domain,
                "osType": get_os_type(),  # 确保操作系统类型是最新的
                "registered": True,
                "register_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
            
            # 保存配置
            if self.save_config():
                self.logger.info("本地配置已同步到服务器数据")
                return True, "同步成功"
            else:
                self.logger.error("保存本地配置失败")
                return False, "保存本地配置失败"
                
        except Exception as e:
            self.logger.error(f"同步失败: {str(e)}")
            return False, f"同步失败: {str(e)}" 