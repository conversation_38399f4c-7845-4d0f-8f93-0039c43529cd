﻿2025-08-01 10:31:59.958 |  INFO 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:86)
2025-08-01 10:31:59.967 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM machines WHERE tenant_id = 1
2025-08-01 10:31:59.967 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:31:59.968 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:31:59.968 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1 ORDER BY create_time DESC, id DESC LIMIT ?
2025-08-01 10:31:59.968 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 10(Long)
2025-08-01 10:31:59.969 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:31:59.970 |  INFO 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/page) 耗时(10 ms)]
2025-08-01 10:32:12.694 |  INFO 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:32:12.697 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:32:12.697 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:32:12.698 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:12.702 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:32:12.702 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:32:12.703 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:32:12.704 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:32:12.705 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:32:12.703789(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:32:12.707 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:32:12.707 |  INFO 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:32:12.709 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:32:12.709 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:32:12.710 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:32:12.710 |  INFO 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:32:13
2025-08-01 10:32:12.710 |  INFO 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:32:12.711 |  INFO 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=false, Redis数据={}, 最后上报时间=2025-08-01T10:31:53, 最终状态=0(在线)
2025-08-01 10:32:12.712 |  INFO 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(18 ms)]
2025-08-01 10:32:15.532 |  INFO 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:86)
2025-08-01 10:32:15.540 | DEBUG 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM machines WHERE tenant_id = 1
2025-08-01 10:32:15.541 | DEBUG 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:32:15.542 | DEBUG 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:32:15.542 | DEBUG 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1 ORDER BY create_time DESC, id DESC LIMIT ?
2025-08-01 10:32:15.542 | DEBUG 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 10(Long)
2025-08-01 10:32:15.543 | DEBUG 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:15.545 |  INFO 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/page) 耗时(12 ms)]
2025-08-01 10:32:16.563 |  INFO 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:86)
2025-08-01 10:32:16.569 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM machines WHERE tenant_id = 1
2025-08-01 10:32:16.569 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:32:16.570 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:32:16.570 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1 ORDER BY create_time DESC, id DESC LIMIT ?
2025-08-01 10:32:16.570 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 10(Long)
2025-08-01 10:32:16.571 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:16.572 |  INFO 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/page) 耗时(9 ms)]
2025-08-01 10:32:17.012 |  INFO 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:86)
2025-08-01 10:32:17.018 | DEBUG 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM machines WHERE tenant_id = 1
2025-08-01 10:32:17.019 | DEBUG 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:32:17.019 | DEBUG 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:32:17.020 | DEBUG 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1 ORDER BY create_time DESC, id DESC LIMIT ?
2025-08-01 10:32:17.020 | DEBUG 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 10(Long)
2025-08-01 10:32:17.020 | DEBUG 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:17.022 |  INFO 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/page) 耗时(9 ms)]
2025-08-01 10:32:17.856 |  INFO 18160 | http-nio-48080-exec-5 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/list) 无参数]
2025-08-01 10:32:17.856 |  INFO 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/mobile/devices/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:94)
	Controller 方法路径：cn.iocoder.yudao.module.mobile.controller.admin.MobileDeviceController(MobileDeviceController.java:77)
2025-08-01 10:32:17.865 | DEBUG 18160 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:32:17.865 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM mobile_devices WHERE tenant_id = 1
2025-08-01 10:32:17.865 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:32:17.865 | DEBUG 18160 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:32:17.866 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:32:17.866 | DEBUG 18160 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:17.867 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE tenant_id = 1 ORDER BY id DESC LIMIT ?
2025-08-01 10:32:17.867 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 10(Long)
2025-08-01 10:32:17.868 |  INFO 18160 | http-nio-48080-exec-5 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/list) 耗时(11 ms)]
2025-08-01 10:32:17.868 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:32:17.871 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:32:17.871 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:32:17.872 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:17.873 |  INFO 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/mobile/devices/page) 耗时(16 ms)]
2025-08-01 10:32:20.528 |  INFO 18160 | http-nio-48080-exec-7 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/mobile/devices/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.mobile.controller.admin.MobileDeviceController(MobileDeviceController.java:77)
2025-08-01 10:32:20.534 | DEBUG 18160 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM mobile_devices WHERE tenant_id = 1
2025-08-01 10:32:20.535 | DEBUG 18160 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:32:20.535 | DEBUG 18160 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:32:20.535 | DEBUG 18160 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE tenant_id = 1 ORDER BY id DESC LIMIT ?
2025-08-01 10:32:20.536 | DEBUG 18160 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 10(Long)
2025-08-01 10:32:20.536 | DEBUG 18160 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:32:20.537 | DEBUG 18160 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:32:20.538 | DEBUG 18160 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:32:20.539 | DEBUG 18160 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:20.540 |  INFO 18160 | http-nio-48080-exec-7 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/mobile/devices/page) 耗时(11 ms)]
2025-08-01 10:32:20.651 |  INFO 18160 | scheduling-1 [TID: N/A] c.i.y.m.m.job.DeviceOfflineCheckJob      | 🔍 开始执行设备离线检测定时任务
2025-08-01 10:32:20.651 | ERROR 18160 | scheduling-1 [TID: N/A] c.i.y.m.m.job.DeviceOfflineCheckJob      | 批量检查代理器离线状态失败

org.mybatis.spring.MyBatisSystemException: 
### Error querying database.  Cause: java.lang.NullPointerException: TenantContextHolder 不存在租户编号！可参考文档：https://doc.iocoder.cn
### Cause: java.lang.NullPointerException: TenantContextHolder 不存在租户编号！可参考文档：https://doc.iocoder.cn
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:99)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy148.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:194)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:155)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:92)
	at jdk.proxy2/jdk.proxy2.$Proxy235.selectList(Unknown Source)
	at cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX.selectList(BaseMapperX.java:138)
	at java.base/java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:732)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:181)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:92)
	at jdk.proxy2/jdk.proxy2.$Proxy235.selectList(Unknown Source)
	at cn.iocoder.yudao.module.machines.service.MachinesServiceImpl.getActiveMachines(MachinesServiceImpl.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at cn.iocoder.yudao.module.machines.service.MachinesServiceImpl$$SpringCGLIB$$0.getActiveMachines(<generated>)
	at cn.iocoder.yudao.module.monitor.job.DeviceOfflineCheckJob.checkMachineOfflineStatus(DeviceOfflineCheckJob.java:76)
	at cn.iocoder.yudao.module.monitor.job.DeviceOfflineCheckJob.checkAndUpdateOfflineDevices(DeviceOfflineCheckJob.java:54)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124)
	at io.micrometer.observation.Observation.observe(Observation.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124)
	at org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.NullPointerException: TenantContextHolder 不存在租户编号！可参考文档：https://doc.iocoder.cn
### Cause: java.lang.NullPointerException: TenantContextHolder 不存在租户编号！可参考文档：https://doc.iocoder.cn
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:156)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at jdk.internal.reflect.GeneratedMethodAccessor35.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 42 common frames omitted
Caused by: java.lang.NullPointerException: TenantContextHolder 不存在租户编号！可参考文档：https://doc.iocoder.cn
	at cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder.getRequiredTenantId(TenantContextHolder.java:41)
	at cn.iocoder.yudao.framework.tenant.core.db.TenantDatabaseInterceptor.getTenantId(TenantDatabaseInterceptor.java:42)
	at com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor.buildTableExpression(TenantLineInnerInterceptor.java:262)
	at com.baomidou.mybatisplus.extension.plugins.inner.BaseMultiTableInnerInterceptor.lambda$builderExpression$3(BaseMultiTableInnerInterceptor.java:384)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.baomidou.mybatisplus.extension.plugins.inner.BaseMultiTableInnerInterceptor.builderExpression(BaseMultiTableInnerInterceptor.java:386)
	at com.baomidou.mybatisplus.extension.plugins.inner.BaseMultiTableInnerInterceptor.processPlainSelect(BaseMultiTableInnerInterceptor.java:115)
	at com.baomidou.mybatisplus.extension.plugins.inner.BaseMultiTableInnerInterceptor.processSelectBody(BaseMultiTableInnerInterceptor.java:56)
	at com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor.processSelect(TenantLineInnerInterceptor.java:86)
	at com.baomidou.mybatisplus.extension.parser.JsqlParserSupport.processParser(JsqlParserSupport.java:90)
	at com.baomidou.mybatisplus.extension.parser.JsqlParserSupport.parserSingle(JsqlParserSupport.java:49)
	at com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor.beforeQuery(TenantLineInnerInterceptor.java:66)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:78)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy261.query(Unknown Source)
	at jdk.internal.reflect.GeneratedMethodAccessor27.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:93)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy261.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	... 48 common frames omitted

2025-08-01 10:32:20.652 | ERROR 18160 | scheduling-1 [TID: N/A] c.i.y.m.m.job.DeviceOfflineCheckJob      | 批量检查手机设备离线状态失败

org.mybatis.spring.MyBatisSystemException: 
### Error querying database.  Cause: java.lang.NullPointerException: TenantContextHolder 不存在租户编号！可参考文档：https://doc.iocoder.cn
### Cause: java.lang.NullPointerException: TenantContextHolder 不存在租户编号！可参考文档：https://doc.iocoder.cn
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:99)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy148.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:194)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:155)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:92)
	at jdk.proxy2/jdk.proxy2.$Proxy234.selectList(Unknown Source)
	at cn.iocoder.yudao.module.mobile.dal.mysql.MobileDeviceMapper.selectList(MobileDeviceMapper.java:43)
	at java.base/java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:732)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:181)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:92)
	at jdk.proxy2/jdk.proxy2.$Proxy234.selectList(Unknown Source)
	at cn.iocoder.yudao.module.mobile.service.MobileDeviceServiceImpl.getActiveDevices(MobileDeviceServiceImpl.java:474)
	at cn.iocoder.yudao.module.monitor.job.DeviceOfflineCheckJob.checkMobileDeviceOfflineStatus(DeviceOfflineCheckJob.java:137)
	at cn.iocoder.yudao.module.monitor.job.DeviceOfflineCheckJob.checkAndUpdateOfflineDevices(DeviceOfflineCheckJob.java:57)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124)
	at io.micrometer.observation.Observation.observe(Observation.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124)
	at org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.NullPointerException: TenantContextHolder 不存在租户编号！可参考文档：https://doc.iocoder.cn
### Cause: java.lang.NullPointerException: TenantContextHolder 不存在租户编号！可参考文档：https://doc.iocoder.cn
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:156)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at jdk.internal.reflect.GeneratedMethodAccessor35.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 31 common frames omitted
Caused by: java.lang.NullPointerException: TenantContextHolder 不存在租户编号！可参考文档：https://doc.iocoder.cn
	at cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder.getRequiredTenantId(TenantContextHolder.java:41)
	at cn.iocoder.yudao.framework.tenant.core.db.TenantDatabaseInterceptor.getTenantId(TenantDatabaseInterceptor.java:42)
	at com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor.buildTableExpression(TenantLineInnerInterceptor.java:262)
	at com.baomidou.mybatisplus.extension.plugins.inner.BaseMultiTableInnerInterceptor.lambda$builderExpression$3(BaseMultiTableInnerInterceptor.java:384)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.baomidou.mybatisplus.extension.plugins.inner.BaseMultiTableInnerInterceptor.builderExpression(BaseMultiTableInnerInterceptor.java:386)
	at com.baomidou.mybatisplus.extension.plugins.inner.BaseMultiTableInnerInterceptor.processPlainSelect(BaseMultiTableInnerInterceptor.java:115)
	at com.baomidou.mybatisplus.extension.plugins.inner.BaseMultiTableInnerInterceptor.processSelectBody(BaseMultiTableInnerInterceptor.java:56)
	at com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor.processSelect(TenantLineInnerInterceptor.java:86)
	at com.baomidou.mybatisplus.extension.parser.JsqlParserSupport.processParser(JsqlParserSupport.java:90)
	at com.baomidou.mybatisplus.extension.parser.JsqlParserSupport.parserSingle(JsqlParserSupport.java:49)
	at com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor.beforeQuery(TenantLineInnerInterceptor.java:66)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:78)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy261.query(Unknown Source)
	at jdk.internal.reflect.GeneratedMethodAccessor27.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:93)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy261.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	... 37 common frames omitted

2025-08-01 10:32:20.652 |  INFO 18160 | scheduling-1 [TID: N/A] c.i.y.m.m.job.DeviceOfflineCheckJob      | ✅ 设备离线检测完成，离线代理器: 0, 离线手机设备: 0
2025-08-01 10:32:20.748 |  INFO 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/mobile/devices/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.mobile.controller.admin.MobileDeviceController(MobileDeviceController.java:77)
2025-08-01 10:32:20.760 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM mobile_devices WHERE tenant_id = 1
2025-08-01 10:32:20.760 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:32:20.761 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:32:20.761 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE tenant_id = 1 ORDER BY id DESC LIMIT ?
2025-08-01 10:32:20.762 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 10(Long)
2025-08-01 10:32:20.763 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:32:20.765 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:32:20.766 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:32:20.767 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:20.769 |  INFO 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/mobile/devices/page) 耗时(21 ms)]
2025-08-01 10:32:20.946 |  INFO 18160 | http-nio-48080-exec-9 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/mobile/devices/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.mobile.controller.admin.MobileDeviceController(MobileDeviceController.java:77)
2025-08-01 10:32:20.955 | DEBUG 18160 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM mobile_devices WHERE tenant_id = 1
2025-08-01 10:32:20.955 | DEBUG 18160 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:32:20.956 | DEBUG 18160 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:32:20.957 | DEBUG 18160 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE tenant_id = 1 ORDER BY id DESC LIMIT ?
2025-08-01 10:32:20.957 | DEBUG 18160 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 10(Long)
2025-08-01 10:32:20.958 | DEBUG 18160 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:32:20.961 | DEBUG 18160 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:32:20.961 | DEBUG 18160 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:32:20.962 | DEBUG 18160 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:20.964 |  INFO 18160 | http-nio-48080-exec-9 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/mobile/devices/page) 耗时(17 ms)]
2025-08-01 10:32:21.941 |  INFO 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:86)
2025-08-01 10:32:21.948 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM machines WHERE tenant_id = 1
2025-08-01 10:32:21.949 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:32:21.950 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:32:21.950 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1 ORDER BY create_time DESC, id DESC LIMIT ?
2025-08-01 10:32:21.950 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 10(Long)
2025-08-01 10:32:21.951 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:21.953 |  INFO 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/page) 耗时(11 ms)]
2025-08-01 10:32:23.423 |  INFO 18160 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务开始]
2025-08-01 10:32:23.425 | DEBUG 18160 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE deleted = 0
2025-08-01 10:32:23.425 | DEBUG 18160 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==> Parameters: 
2025-08-01 10:32:23.427 | DEBUG 18160 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | <==      Total: 3
2025-08-01 10:32:23.434 |  INFO 18160 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:32:23.438 |  INFO 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:32:23.441 | DEBUG 18160 | pool-3-thread-4 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:32:23.441 | DEBUG 18160 | pool-3-thread-4 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 1(Long)
2025-08-01 10:32:23.442 | DEBUG 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:32:23.442 | DEBUG 18160 | pool-3-thread-4 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:32:23.442 | DEBUG 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:32:23.443 | DEBUG 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:23.446 | DEBUG 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:32:23.446 | DEBUG 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:32:23.447 | DEBUG 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:32:23.449 | DEBUG 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:32:23.449 | DEBUG 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:32:23.448174(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:32:23.451 | DEBUG 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:32:23.451 |  INFO 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:32:23.454 | DEBUG 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:32:23.454 | DEBUG 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:32:23.455 | DEBUG 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:32:23.455 |  INFO 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:32:23
2025-08-01 10:32:23.455 |  INFO 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:32:23.456 |  INFO 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=false, Redis数据={}, 最后上报时间=2025-08-01T10:32:13, 最终状态=0(在线)
2025-08-01 10:32:23.458 |  INFO 18160 | http-nio-48080-exec-4 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(19 ms)]
2025-08-01 10:32:23.458 |  INFO 18160 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:32:23.458 |  INFO 18160 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:32:23.464 |  INFO 18160 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:32:23.466 |  INFO 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
2025-08-01 10:32:23.467 | DEBUG 18160 | pool-3-thread-4 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:32:23.468 | DEBUG 18160 | pool-3-thread-4 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 121(Long)
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:32:23.469 | DEBUG 18160 | pool-3-thread-4 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:32:23.470 | DEBUG 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 121
2025-08-01 10:32:23.470 | DEBUG 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:32:23.471 | DEBUG 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 0
2025-08-01 10:32:23.472 |  INFO 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(5 ms)]
2025-08-01 10:32:23.473 |  INFO 18160 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:32:23.473 |  INFO 18160 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:32:23.480 |  INFO 18160 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:32:23.482 |  WARN 18160 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.w.c.h.GlobalExceptionHandler     | [serviceExceptionHandler]
	cn.iocoder.yudao.module.system.service.tenant.TenantServiceImpl.validTenant(TenantServiceImpl.java:94)
2025-08-01 10:32:23.483 | DEBUG 18160 | pool-3-thread-4 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:32:23.483 | DEBUG 18160 | pool-3-thread-4 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 122(Long)
2025-08-01 10:32:23.484 |  WARN 18160 | scheduling-1 [TID: N/A] c.i.y.m.channel.api.MonitorApiClient     | 获取设备列表失败: 名字为【测试租户】的租户已过期
2025-08-01 10:32:23.484 |  INFO 18160 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:32:23.484 |  INFO 18160 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:32:23.484 |  INFO 18160 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务结束]
2025-08-01 10:32:23.484 | DEBUG 18160 | pool-3-thread-4 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:32:24.588 |  INFO 18160 | http-nio-48080-exec-5 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:86)
2025-08-01 10:32:24.595 | DEBUG 18160 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM machines WHERE tenant_id = 1
2025-08-01 10:32:24.595 | DEBUG 18160 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:32:24.596 | DEBUG 18160 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:32:24.596 | DEBUG 18160 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1 ORDER BY create_time DESC, id DESC LIMIT ?
2025-08-01 10:32:24.596 | DEBUG 18160 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 10(Long)
2025-08-01 10:32:24.597 | DEBUG 18160 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:24.598 |  INFO 18160 | http-nio-48080-exec-5 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/page) 耗时(10 ms)]
2025-08-01 10:32:24.829 |  INFO 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:86)
2025-08-01 10:32:24.837 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM machines WHERE tenant_id = 1
2025-08-01 10:32:24.838 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:32:24.839 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:32:24.840 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1 ORDER BY create_time DESC, id DESC LIMIT ?
2025-08-01 10:32:24.840 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 10(Long)
2025-08-01 10:32:24.841 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:24.843 |  INFO 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/page) 耗时(13 ms)]
2025-08-01 10:32:25.070 |  INFO 18160 | http-nio-48080-exec-7 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:86)
2025-08-01 10:32:25.081 | DEBUG 18160 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM machines WHERE tenant_id = 1
2025-08-01 10:32:25.081 | DEBUG 18160 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:32:25.083 | DEBUG 18160 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:32:25.083 | DEBUG 18160 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1 ORDER BY create_time DESC, id DESC LIMIT ?
2025-08-01 10:32:25.083 | DEBUG 18160 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 10(Long)
2025-08-01 10:32:25.085 | DEBUG 18160 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:25.087 |  INFO 18160 | http-nio-48080-exec-7 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/page) 耗时(16 ms)]
2025-08-01 10:32:25.294 |  INFO 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:86)
2025-08-01 10:32:25.303 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM machines WHERE tenant_id = 1
2025-08-01 10:32:25.304 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:32:25.305 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:32:25.306 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1 ORDER BY create_time DESC, id DESC LIMIT ?
2025-08-01 10:32:25.306 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 10(Long)
2025-08-01 10:32:25.307 | DEBUG 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:25.309 |  INFO 18160 | http-nio-48080-exec-8 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/page) 耗时(14 ms)]
2025-08-01 10:32:26.234 |  INFO 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:32:26.236 | DEBUG 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:32:26.236 | DEBUG 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:32:26.237 | DEBUG 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:26.239 | DEBUG 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:32:26.240 | DEBUG 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:32:26.241 | DEBUG 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:32:26.241 | DEBUG 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:32:26.241 | DEBUG 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:32:26.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:32:26.243 | DEBUG 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:32:26.243 |  INFO 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:32:26.244 | DEBUG 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:32:26.244 | DEBUG 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:32:26.245 | DEBUG 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:32:26.245 |  INFO 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:32:26
2025-08-01 10:32:26.245 |  INFO 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:32:26.246 |  INFO 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=false, Redis数据={}, 最后上报时间=2025-08-01T10:32:23, 最终状态=0(在线)
2025-08-01 10:32:26.246 |  INFO 18160 | http-nio-48080-exec-1 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(13 ms)]
2025-08-01 10:32:30.961 |  INFO 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:32:30.966 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:32:30.966 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:32:30.967 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:30.971 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:32:30.971 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:32:30.973 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:32:30.975 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:32:30.976 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:32:30.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:32:30.978 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:32:30.978 |  INFO 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:32:30.980 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:32:30.981 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:32:30.982 | DEBUG 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:32:30.982 |  INFO 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:32:31
2025-08-01 10:32:30.982 |  INFO 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:32:30.982 |  INFO 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=false, Redis数据={}, 最后上报时间=2025-08-01T10:32:26, 最终状态=0(在线)
2025-08-01 10:32:30.983 |  INFO 18160 | http-nio-48080-exec-2 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(21 ms)]
2025-08-01 10:32:31.370 |  INFO 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:32:31.373 | DEBUG 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:32:31.373 | DEBUG 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:32:31.374 | DEBUG 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:31.376 | DEBUG 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:32:31.376 | DEBUG 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:32:31.377 | DEBUG 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:32:31.379 | DEBUG 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:32:31.379 | DEBUG 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:32:31.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:32:31.380 | DEBUG 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:32:31.381 |  INFO 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:32:31.382 | DEBUG 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:32:31.382 | DEBUG 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:32:31.383 | DEBUG 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:32:31.383 |  INFO 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:32:31
2025-08-01 10:32:31.383 |  INFO 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:32:31.384 |  INFO 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=false, Redis数据={}, 最后上报时间=2025-08-01T10:32:31, 最终状态=0(在线)
2025-08-01 10:32:31.386 |  INFO 18160 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(15 ms)]
2025-08-01 10:32:31.578 |  INFO 18160 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:32:31.580 | DEBUG 18160 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:32:31.580 | DEBUG 18160 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:32:31.581 | DEBUG 18160 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:31.583 | DEBUG 18160 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:32:31.583 | DEBUG 18160 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:32:31.584 | DEBUG 18160 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:32:31.585 | DEBUG 18160 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:32:31.585 | DEBUG 18160 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:32:31.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:32:31.587 | DEBUG 18160 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:32:31.587 |  INFO 18160 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:32:31.589 | DEBUG 18160 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:32:31.589 | DEBUG 18160 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:32:31.590 | DEBUG 18160 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:32:31.591 |  INFO 18160 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:32:32
2025-08-01 10:32:31.591 |  INFO 18160 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:32:31.592 |  INFO 18160 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=false, Redis数据={}, 最后上报时间=2025-08-01T10:32:31, 最终状态=0(在线)
2025-08-01 10:32:31.593 |  INFO 18160 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(15 ms)]
2025-08-01 10:32:42.085 |  INFO 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:32:42.088 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:32:42.088 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:32:42.089 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:32:42.092 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:32:42.092 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:32:42.093 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:32:42.094 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:32:42.095 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:32:42.093374(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:32:42.096 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:32:42.097 |  INFO 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:32:42.099 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:32:42.099 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:32:42.100 | DEBUG 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:32:42.100 |  INFO 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:32:42
2025-08-01 10:32:42.100 |  INFO 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:32:42.101 |  INFO 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=false, Redis数据={}, 最后上报时间=2025-08-01T10:32:32, 最终状态=0(在线)
2025-08-01 10:32:42.102 |  INFO 18160 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(16 ms)]
