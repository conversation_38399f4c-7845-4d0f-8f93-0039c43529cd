﻿2025-08-01 10:46:15.132 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/status/3d95b6bb04a7e5bf8dddcd691d869b70) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesReportController(MachinesReportController.java:85)
2025-08-01 10:46:15.164 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.service.MachinesServiceImpl    | [queryMachinesStatus] 开始查询代理器状态，machineId: 3d95b6bb04a7e5bf8dddcd691d869b70
2025-08-01 10:46:15.172 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?)
2025-08-01 10:46:15.173 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:46:15.179 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:46:15.180 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.service.MachinesServiceImpl    | [queryMachinesStatus] 查询结果: MachinesDO(super=PhysicalDeleteBaseDO(tenantId=1, createTime=2025-08-01T09:40:31, updateTime=2025-08-01T10:46:02, creator=system, updater=null), id=20, machineId=3d95b6bb04a7e5bf8dddcd691d869b70, name=null, status=1, osType=windows, runDays=0, registerTime=2025-08-01T09:40:31, lastReportTime=2025-08-01T10:30:11, remark=null, cloudDomain=http://localhost:48080)
2025-08-01 10:46:15.204 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/status/3d95b6bb04a7e5bf8dddcd691d869b70) 耗时(71 ms)]
2025-08-01 10:46:19.470 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/status/3d95b6bb04a7e5bf8dddcd691d869b70) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesReportController(MachinesReportController.java:85)
2025-08-01 10:46:19.474 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.service.MachinesServiceImpl    | [queryMachinesStatus] 开始查询代理器状态，machineId: 3d95b6bb04a7e5bf8dddcd691d869b70
2025-08-01 10:46:19.478 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?)
2025-08-01 10:46:19.480 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:46:19.482 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:46:19.482 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.service.MachinesServiceImpl    | [queryMachinesStatus] 查询结果: MachinesDO(super=PhysicalDeleteBaseDO(tenantId=1, createTime=2025-08-01T09:40:31, updateTime=2025-08-01T10:46:02, creator=system, updater=null), id=20, machineId=3d95b6bb04a7e5bf8dddcd691d869b70, name=null, status=1, osType=windows, runDays=0, registerTime=2025-08-01T09:40:31, lastReportTime=2025-08-01T10:30:11, remark=null, cloudDomain=http://localhost:48080)
2025-08-01 10:46:19.485 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/status/3d95b6bb04a7e5bf8dddcd691d869b70) 耗时(14 ms)]
2025-08-01 10:46:19.499 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/client-status/3d95b6bb04a7e5bf8dddcd691d869b70) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesReportController(MachinesReportController.java:152)
2025-08-01 10:46:19.503 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.service.MachinesServiceImpl    | [queryMachinesStatus] 开始查询代理器状态，machineId: 3d95b6bb04a7e5bf8dddcd691d869b70
2025-08-01 10:46:19.505 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?)
2025-08-01 10:46:19.505 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:46:19.508 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:46:19.509 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.service.MachinesServiceImpl    | [queryMachinesStatus] 查询结果: MachinesDO(super=PhysicalDeleteBaseDO(tenantId=1, createTime=2025-08-01T09:40:31, updateTime=2025-08-01T10:46:02, creator=system, updater=null), id=20, machineId=3d95b6bb04a7e5bf8dddcd691d869b70, name=null, status=1, osType=windows, runDays=0, registerTime=2025-08-01T09:40:31, lastReportTime=2025-08-01T10:30:11, remark=null, cloudDomain=http://localhost:48080)
2025-08-01 10:46:19.510 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.c.a.MachinesReportController   | 开始查询手机设备: machineId=3d95b6bb04a7e5bf8dddcd691d869b70, tenantId=1
2025-08-01 10:46:19.519 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?)
2025-08-01 10:46:19.520 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:46:19.522 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:46:19.522 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.c.a.MachinesReportController   | 查询到设备数量: 1
2025-08-01 10:46:19.522 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.c.a.MachinesReportController   | 查询到手机设备数量: 1
2025-08-01 10:46:19.522 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.c.a.MachinesReportController   | 查询客户端状态成功: machineId=3d95b6bb04a7e5bf8dddcd691d869b70, tenantId=1
2025-08-01 10:46:19.525 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/client-status/3d95b6bb04a7e5bf8dddcd691d869b70) 耗时(26 ms)]
2025-08-01 10:46:19.672 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.c.w.AgentWebSocketEndpoint       | [onOpen][新的WebSocket连接] sessionId: 0, 当前在线数: 1, 远程地址: {}, 协议: 
2025-08-01 10:46:19.691 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.c.w.AgentWebSocketEndpoint       | [onMessage][收到消息] sessionId: 0, message: {"type": "machine_register", "machineId": "3d95b6bb04a7e5bf8dddcd691d869b70", "timestamp": 1754016379635}
2025-08-01 10:46:19.701 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.c.w.AgentWebSocketEndpoint       | [extractAndStoreMachineId][存储机器ID映射] sessionId: 0, machineId: 3d95b6bb04a7e5bf8dddcd691d869b70
2025-08-01 10:46:28.163 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.s.d.m.o.O.selectList             | ==>  Preparing: SELECT id, access_token, refresh_token, user_id, user_type, user_info, client_id, scopes, expires_time, tenant_id, create_time, update_time, creator, updater, deleted FROM system_oauth2_access_token WHERE deleted = 0 AND (access_token = ?)
2025-08-01 10:46:28.164 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.s.d.m.o.O.selectList             | ==> Parameters: 036c0b956be34589a7974124a89634b7(String)
2025-08-01 10:46:28.167 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.s.d.m.o.O.selectList             | <==      Total: 1
2025-08-01 10:46:28.195 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.o.O.selectList             | ==>  Preparing: SELECT id, access_token, refresh_token, user_id, user_type, user_info, client_id, scopes, expires_time, tenant_id, create_time, update_time, creator, updater, deleted FROM system_oauth2_access_token WHERE deleted = 0 AND (access_token = ?)
2025-08-01 10:46:28.196 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.o.O.selectList             | ==> Parameters: 036c0b956be34589a7974124a89634b7(String)
2025-08-01 10:46:28.199 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.o.O.selectList             | <==      Total: 1
2025-08-01 10:46:28.202 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:46:28.215 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:46:28.215 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:46:28.217 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:46:28.230 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:46:28.231 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:46:28.234 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:46:28.238 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:46:28.239 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:46:28.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:46:28.246 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:46:28.246 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:46:28.255 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:46:28.256 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:46:28.261 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:46:28.262 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:46:28
2025-08-01 10:46:28.263 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:46:28.264 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=2, Redis在线=false, Redis数据={}, 最后上报时间=2025-08-01T10:46:03, 最终状态=0(在线)
2025-08-01 10:46:28.267 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(64 ms)]
2025-08-01 10:46:28.280 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/system/auth/refresh-token) 参数({refreshToken=221aa7c5cf8841ecaa5e1a9b1dd1da82})]
	Controller 方法路径：cn.iocoder.yudao.module.system.controller.admin.auth.AuthController(AuthController.java:88)
2025-08-01 10:46:28.367 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.s.d.m.o.O.selectList             | ==>  Preparing: SELECT id, refresh_token, user_id, user_type, client_id, scopes, expires_time, tenant_id, create_time, update_time, creator, updater, deleted FROM system_oauth2_refresh_token WHERE deleted = 0 AND (refresh_token = ?)
2025-08-01 10:46:28.368 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.s.d.m.o.O.selectList             | ==> Parameters: 221aa7c5cf8841ecaa5e1a9b1dd1da82(String)
2025-08-01 10:46:28.371 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.s.d.m.o.O.selectList             | <==      Total: 1
2025-08-01 10:46:28.417 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.s.d.m.o.O.selectList             | ==>  Preparing: SELECT id, client_id, secret, name, logo, description, status, access_token_validity_seconds, refresh_token_validity_seconds, redirect_uris, authorized_grant_types, scopes, auto_approve_scopes, authorities, resource_ids, additional_information, create_time, update_time, creator, updater, deleted FROM system_oauth2_client WHERE deleted = 0 AND (client_id = ?)
2025-08-01 10:46:28.419 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.s.d.m.o.O.selectList             | ==> Parameters: default(String)
2025-08-01 10:46:28.424 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.s.d.m.o.O.selectList             | <==      Total: 1
2025-08-01 10:46:28.539 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.s.d.m.o.O.selectList             | ==>  Preparing: SELECT id, access_token, refresh_token, user_id, user_type, user_info, client_id, scopes, expires_time, tenant_id, create_time, update_time, creator, updater, deleted FROM system_oauth2_access_token WHERE deleted = 0 AND (refresh_token = ?) AND tenant_id = 1
2025-08-01 10:46:28.541 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.s.d.m.o.O.selectList             | ==> Parameters: 221aa7c5cf8841ecaa5e1a9b1dd1da82(String)
2025-08-01 10:46:28.547 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.s.d.m.o.O.selectList             | <==      Total: 1
2025-08-01 10:46:28.574 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.s.d.m.o.O.deleteByIds            | ==>  Preparing: UPDATE system_oauth2_access_token SET update_time = ?, updater = ?, deleted = 1 WHERE id IN (?) AND deleted = 0 AND tenant_id = 1
2025-08-01 10:46:28.575 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.s.d.m.o.O.deleteByIds            | ==> Parameters: 2025-08-01T10:46:28.564951800(LocalDateTime), null, 18315(Long)
2025-08-01 10:46:28.581 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.s.d.m.o.O.deleteByIds            | <==    Updates: 1
2025-08-01 10:46:28.601 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.s.d.m.u.A.selectById             | ==>  Preparing: SELECT id, username, password, nickname, remark, dept_id, post_ids, email, mobile, sex, avatar, status, login_ip, login_date, tenant_id, create_time, update_time, creator, updater, deleted FROM system_users WHERE id = ? AND deleted = 0 AND tenant_id = 1
2025-08-01 10:46:28.601 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.s.d.m.u.A.selectById             | ==> Parameters: 1(Long)
2025-08-01 10:46:28.605 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.s.d.m.u.A.selectById             | <==      Total: 1
2025-08-01 10:46:28.622 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.s.d.m.o.O.insert                 | ==>  Preparing: INSERT INTO system_oauth2_access_token (access_token, refresh_token, user_id, user_type, user_info, client_id, expires_time, tenant_id, create_time, update_time, creator, updater) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-08-01 10:46:28.627 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.s.d.m.o.O.insert                 | ==> Parameters: 872f205528874e41926f80717b3771e9(String), 221aa7c5cf8841ecaa5e1a9b1dd1da82(String), 1(Long), 2(Integer), {"nickname":"芋道源码","deptId":"100"}(String), default(String), 2025-08-01T11:16:28.606838400(LocalDateTime), 1(Long), 2025-08-01T10:46:28.612827200(LocalDateTime), 2025-08-01T10:46:28.612827200(LocalDateTime), null, null
2025-08-01 10:46:28.632 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.s.d.m.o.O.insert                 | <==    Updates: 1
2025-08-01 10:46:28.660 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/system/auth/refresh-token) 耗时(379 ms)]
2025-08-01 10:46:28.784 |  INFO 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/system/notify-message/get-unread-count) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.system.controller.admin.notify.NotifyMessageController(NotifyMessageController.java:93)
2025-08-01 10:46:28.890 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.s.d.m.n.N.selectCount            | ==>  Preparing: SELECT COUNT(*) AS total FROM system_notify_message WHERE deleted = 0 AND (read_status = ? AND user_id = ? AND user_type = ?) AND tenant_id = 1
2025-08-01 10:46:28.892 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.s.d.m.n.N.selectCount            | ==> Parameters: false(Boolean), 1(Long), 2(Integer)
2025-08-01 10:46:28.900 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.s.d.m.n.N.selectCount            | <==      Total: 1
2025-08-01 10:46:28.903 |  INFO 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/system/notify-message/get-unread-count) 耗时(119 ms)]
2025-08-01 10:46:30.641 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/status/3d95b6bb04a7e5bf8dddcd691d869b70) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesReportController(MachinesReportController.java:85)
2025-08-01 10:46:30.645 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.service.MachinesServiceImpl    | [queryMachinesStatus] 开始查询代理器状态，machineId: 3d95b6bb04a7e5bf8dddcd691d869b70
2025-08-01 10:46:30.650 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?)
2025-08-01 10:46:30.651 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:46:30.653 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:46:30.653 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.service.MachinesServiceImpl    | [queryMachinesStatus] 查询结果: MachinesDO(super=PhysicalDeleteBaseDO(tenantId=1, createTime=2025-08-01T09:40:31, updateTime=2025-08-01T10:46:02, creator=system, updater=null), id=20, machineId=3d95b6bb04a7e5bf8dddcd691d869b70, name=null, status=1, osType=windows, runDays=0, registerTime=2025-08-01T09:40:31, lastReportTime=2025-08-01T10:30:11, remark=null, cloudDomain=http://localhost:48080)
2025-08-01 10:46:30.655 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/status/3d95b6bb04a7e5bf8dddcd691d869b70) 耗时(14 ms)]
2025-08-01 10:46:30.665 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/client-status/3d95b6bb04a7e5bf8dddcd691d869b70) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesReportController(MachinesReportController.java:152)
2025-08-01 10:46:30.668 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.service.MachinesServiceImpl    | [queryMachinesStatus] 开始查询代理器状态，machineId: 3d95b6bb04a7e5bf8dddcd691d869b70
2025-08-01 10:46:30.670 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?)
2025-08-01 10:46:30.671 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:46:30.673 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:46:30.673 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.service.MachinesServiceImpl    | [queryMachinesStatus] 查询结果: MachinesDO(super=PhysicalDeleteBaseDO(tenantId=1, createTime=2025-08-01T09:40:31, updateTime=2025-08-01T10:46:02, creator=system, updater=null), id=20, machineId=3d95b6bb04a7e5bf8dddcd691d869b70, name=null, status=1, osType=windows, runDays=0, registerTime=2025-08-01T09:40:31, lastReportTime=2025-08-01T10:30:11, remark=null, cloudDomain=http://localhost:48080)
2025-08-01 10:46:30.674 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.c.a.MachinesReportController   | 开始查询手机设备: machineId=3d95b6bb04a7e5bf8dddcd691d869b70, tenantId=1
2025-08-01 10:46:30.680 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?)
2025-08-01 10:46:30.681 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:46:30.683 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:46:30.684 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.c.a.MachinesReportController   | 查询到设备数量: 1
2025-08-01 10:46:30.684 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.c.a.MachinesReportController   | 查询到手机设备数量: 1
2025-08-01 10:46:30.684 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.c.a.MachinesReportController   | 查询客户端状态成功: machineId=3d95b6bb04a7e5bf8dddcd691d869b70, tenantId=1
2025-08-01 10:46:30.686 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/client-status/3d95b6bb04a7e5bf8dddcd691d869b70) 耗时(20 ms)]
2025-08-01 10:46:30.866 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:46:30.873 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:46:30.874 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:46:30.879 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:46:30.885 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:46:30.886 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:46:30.889 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:46:30.896 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:46:30.899 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:46:30.890684(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:46:30.903 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:46:30.904 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:46:30.908 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:46:30.909 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:46:30.915 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:46:30.916 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:46:31
2025-08-01 10:46:30.916 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:46:30.919 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=2, Redis在线=false, Redis数据={}, 最后上报时间=2025-08-01T10:46:28, 最终状态=0(在线)
2025-08-01 10:46:30.926 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(60 ms)]
2025-08-01 10:46:33.039 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务开始]
2025-08-01 10:46:33.048 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE deleted = 0
2025-08-01 10:46:33.049 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==> Parameters: 
2025-08-01 10:46:33.052 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | <==      Total: 3
2025-08-01 10:46:33.079 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/broagent/api/devices) 参数({"devices": [{"device": "9DRKU4X4VS69YXFI", "machine": "3d95b6bb04a7e5bf8dddcd691d869b70", "douyinStatus": 2, "xiaohongshuStatus": 2, "wechatStatus": 0, "qywxStatus": 2}], "timestamp": "2025-08-01T10:46:33.063323", "client_version": "1.0.0"})]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceReportController(DeviceReportController.java:34)
2025-08-01 10:46:33.085 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:46:33.099 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:46:33.104 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:46:33.105 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:46:33.109 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:46:33.119 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:46:33.120 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:46:33.122 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:46:33.130 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:46:33.131 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:46:33.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:46:33.134 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:46:33.135 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:46:33.138 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:46:33.138 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:46:33.142 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:46:33.143 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:46:33
2025-08-01 10:46:33.143 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:46:33.147 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=2, Redis在线=false, Redis数据={}, 最后上报时间=2025-08-01T10:46:31, 最终状态=0(在线)
2025-08-01 10:46:33.149 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(50 ms)]
2025-08-01 10:46:33.151 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:46:33.151 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:46:33.173 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:46:33.183 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
2025-08-01 10:46:33.172 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.c.DeviceReportController       | 接收设备上报请求: DeviceReportReqDTO(devices=[DeviceReportReqDTO.DeviceInfo(device=9DRKU4X4VS69YXFI, machine=3d95b6bb04a7e5bf8dddcd691d869b70, douyinStatus=2, xiaohongshuStatus=2, videoStatus=null, officialAccountStatus=null, qywxStatus=2, wechatStatus=0, dingtalkStatus=null, feishuStatus=null)])
2025-08-01 10:46:33.183 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 接收到设备上报数据: DeviceReportReqDTO(devices=[DeviceReportReqDTO.DeviceInfo(device=9DRKU4X4VS69YXFI, machine=3d95b6bb04a7e5bf8dddcd691d869b70, douyinStatus=2, xiaohongshuStatus=2, videoStatus=null, officialAccountStatus=null, qywxStatus=2, wechatStatus=0, dingtalkStatus=null, feishuStatus=null)])
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:46:33.191 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 121
2025-08-01 10:46:33.192 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:46:33.196 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 0
2025-08-01 10:46:33.200 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(17 ms)]
2025-08-01 10:46:33.202 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:46:33.202 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:46:33.204 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:46:33.204 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:46:33.208 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:46:33.217 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.mysql.MachinesMapper.update  | ==>  Preparing: UPDATE machines SET machine_id = ?, last_report_time = ?, update_time = ?, updater = ? WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:46:33.218 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.mysql.MachinesMapper.update  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String), 2025-08-01T10:46:33.210929700(LocalDateTime), 2025-08-01T10:46:33.214916500(LocalDateTime), null, 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:46:33.221 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.mysql.MachinesMapper.update  | <==    Updates: 1
2025-08-01 10:46:33.223 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:46:33.224 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:46:33.226 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:46:33.222897200(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:46:33.230 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:46:33.231 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:46:33.232 |  WARN 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.f.w.c.h.GlobalExceptionHandler     | [serviceExceptionHandler]
	cn.iocoder.yudao.module.system.service.tenant.TenantServiceImpl.validTenant(TenantServiceImpl.java:94)
2025-08-01 10:46:33.234 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:46:33.235 |  WARN 37956 | scheduling-1 [TID: N/A] c.i.y.m.channel.api.MonitorApiClient     | 获取设备列表失败: 名字为【测试租户】的租户已过期
2025-08-01 10:46:33.235 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:46:33.235 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:46:33.235 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:46:33.235 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务结束]
2025-08-01 10:46:33.237 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:46:33.237 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:46:33
2025-08-01 10:46:33.238 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI
2025-08-01 10:46:33.242 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:46:33.243 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:46:33.248 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:46:33.253 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.updateById  | ==>  Preparing: UPDATE machines SET status = ?, update_time = ?, updater = ? WHERE id = ? AND tenant_id = 1
2025-08-01 10:46:33.254 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.updateById  | ==> Parameters: 0(Integer), 2025-08-01T10:46:33.250723(LocalDateTime), null, 20(Long)
2025-08-01 10:46:33.258 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.updateById  | <==    Updates: 1
2025-08-01 10:46:33.260 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.service.MachinesServiceImpl    | 🔥 离线检测：更新代理器状态 id=20, status=0
2025-08-01 10:46:33.270 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:46:33.271 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:46:33.274 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:46:33.281 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.updateById               | ==>  Preparing: UPDATE mobile_devices SET status = ?, update_time = ?, updater = ? WHERE id = ? AND tenant_id = 1
2025-08-01 10:46:33.283 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.updateById               | ==> Parameters: 0(Integer), 2025-08-01T10:46:33.*********(LocalDateTime), null, 17(Long)
2025-08-01 10:46:33.286 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.updateById               | <==    Updates: 1
2025-08-01 10:46:33.286 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔥 离线检测：更新手机设备状态 id=17, status=0
2025-08-01 10:46:33.289 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:46:33.290 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:46:33.297 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:46:33.301 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:46:33.301 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:46:33.304 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:46:33.307 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/broagent/api/devices) 耗时(227 ms)]
2025-08-01 10:46:41.969 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:46:41.978 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:46:41.979 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:46:41.982 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:46:41.988 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:46:41.989 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:46:41.992 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:46:41.999 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:46:42.000 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:46:41.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:46:42.002 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:46:42.003 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:46:42.007 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:46:42.008 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:46:42.010 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:46:42.011 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:46:42
2025-08-01 10:46:42.012 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:46:42.014 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=true, Redis数据={machineId=3d95b6bb04a7e5bf8dddcd691d869b70, lastHeartbeatTime=2025-08-01T10:46:33.186989600}, 最后上报时间=2025-08-01T10:46:33, 最终状态=0(在线)
2025-08-01 10:46:42.016 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(46 ms)]
2025-08-01 10:46:43.750 |  INFO 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:46:43.754 | DEBUG 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:46:43.754 | DEBUG 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:46:43.755 | DEBUG 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:46:43.759 | DEBUG 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:46:43.760 | DEBUG 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:46:43.763 | DEBUG 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:46:43.766 | DEBUG 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:46:43.767 | DEBUG 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:46:43.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:46:43.770 | DEBUG 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:46:43.770 |  INFO 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:46:43.772 | DEBUG 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:46:43.773 | DEBUG 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:46:43.776 | DEBUG 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:46:43.778 |  INFO 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:46:44
2025-08-01 10:46:43.778 |  INFO 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:46:43.779 |  INFO 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=true, Redis数据={machineId=3d95b6bb04a7e5bf8dddcd691d869b70, lastHeartbeatTime=2025-08-01T10:46:33.186989600}, 最后上报时间=2025-08-01T10:46:42, 最终状态=0(在线)
2025-08-01 10:46:43.782 |  INFO 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(31 ms)]
2025-08-01 10:46:45.677 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:86)
2025-08-01 10:46:45.727 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.U.selectList             | ==>  Preparing: SELECT id, user_id, role_id, create_time, update_time, creator, updater, deleted FROM system_user_role WHERE deleted = 0 AND (user_id = ?) AND tenant_id = 1
2025-08-01 10:46:45.729 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.U.selectList             | ==> Parameters: 1(Long)
2025-08-01 10:46:45.732 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.U.selectList             | <==      Total: 2
2025-08-01 10:46:45.746 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.RoleMapper.selectById    | ==>  Preparing: SELECT id, name, code, sort, status, type, remark, data_scope, data_scope_dept_ids, tenant_id, create_time, update_time, creator, updater, deleted FROM system_role WHERE id = ? AND deleted = 0 AND tenant_id = 1
2025-08-01 10:46:45.747 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.RoleMapper.selectById    | ==> Parameters: 1(Long)
2025-08-01 10:46:45.749 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.RoleMapper.selectById    | <==      Total: 1
2025-08-01 10:46:45.756 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.RoleMapper.selectById    | ==>  Preparing: SELECT id, name, code, sort, status, type, remark, data_scope, data_scope_dept_ids, tenant_id, create_time, update_time, creator, updater, deleted FROM system_role WHERE id = ? AND deleted = 0 AND tenant_id = 1
2025-08-01 10:46:45.756 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.RoleMapper.selectById    | ==> Parameters: 2(Long)
2025-08-01 10:46:45.761 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.RoleMapper.selectById    | <==      Total: 1
2025-08-01 10:46:45.774 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.MenuMapper.selectList    | ==>  Preparing: SELECT id, name, permission, type, sort, parent_id, path, icon, component, component_name, status, visible, keep_alive, always_show, create_time, update_time, creator, updater, deleted FROM system_menu WHERE deleted = 0 AND (permission = ?)
2025-08-01 10:46:45.775 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.MenuMapper.selectList    | ==> Parameters: aiphoneagent:machines:query(String)
2025-08-01 10:46:45.780 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.MenuMapper.selectList    | <==      Total: 4
2025-08-01 10:46:45.797 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | ==>  Preparing: SELECT id, role_id, menu_id, tenant_id, create_time, update_time, creator, updater, deleted FROM system_role_menu WHERE deleted = 0 AND (menu_id = ?) AND tenant_id = 1
2025-08-01 10:46:45.798 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | ==> Parameters: 9221(Long)
2025-08-01 10:46:45.800 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | <==      Total: 0
2025-08-01 10:46:45.809 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | ==>  Preparing: SELECT id, role_id, menu_id, tenant_id, create_time, update_time, creator, updater, deleted FROM system_role_menu WHERE deleted = 0 AND (menu_id = ?) AND tenant_id = 1
2025-08-01 10:46:45.810 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | ==> Parameters: 9222(Long)
2025-08-01 10:46:45.813 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | <==      Total: 0
2025-08-01 10:46:45.819 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | ==>  Preparing: SELECT id, role_id, menu_id, tenant_id, create_time, update_time, creator, updater, deleted FROM system_role_menu WHERE deleted = 0 AND (menu_id = ?) AND tenant_id = 1
2025-08-01 10:46:45.820 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | ==> Parameters: 9248(Long)
2025-08-01 10:46:45.823 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | <==      Total: 0
2025-08-01 10:46:45.832 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | ==>  Preparing: SELECT id, role_id, menu_id, tenant_id, create_time, update_time, creator, updater, deleted FROM system_role_menu WHERE deleted = 0 AND (menu_id = ?) AND tenant_id = 1
2025-08-01 10:46:45.833 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | ==> Parameters: 9249(Long)
2025-08-01 10:46:45.834 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | <==      Total: 0
2025-08-01 10:46:45.911 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM machines WHERE tenant_id = 1
2025-08-01 10:46:45.912 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:46:45.915 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:46:45.929 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1 ORDER BY create_time DESC, id DESC LIMIT ?
2025-08-01 10:46:45.930 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 10(Long)
2025-08-01 10:46:45.936 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:46:45.956 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/page) 耗时(278 ms)]
2025-08-01 10:46:47.272 |  INFO 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/list) 无参数]
2025-08-01 10:46:47.272 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/mobile/devices/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.mobile.controller.admin.MobileDeviceController(MobileDeviceController.java:77)
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:94)
2025-08-01 10:46:47.288 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.s.d.m.p.MenuMapper.selectList    | ==>  Preparing: SELECT id, name, permission, type, sort, parent_id, path, icon, component, component_name, status, visible, keep_alive, always_show, create_time, update_time, creator, updater, deleted FROM system_menu WHERE deleted = 0 AND (permission = ?)
2025-08-01 10:46:47.288 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.s.d.m.p.MenuMapper.selectList    | ==> Parameters: aiphoneagent:mobile:query(String)
2025-08-01 10:46:47.293 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.s.d.m.p.MenuMapper.selectList    | <==      Total: 4
2025-08-01 10:46:47.295 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:46:47.295 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:46:47.297 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:46:47.299 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | ==>  Preparing: SELECT id, role_id, menu_id, tenant_id, create_time, update_time, creator, updater, deleted FROM system_role_menu WHERE deleted = 0 AND (menu_id = ?) AND tenant_id = 1
2025-08-01 10:46:47.300 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | ==> Parameters: 9227(Long)
2025-08-01 10:46:47.300 |  INFO 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/list) 耗时(29 ms)]
2025-08-01 10:46:47.302 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | <==      Total: 0
2025-08-01 10:46:47.308 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | ==>  Preparing: SELECT id, role_id, menu_id, tenant_id, create_time, update_time, creator, updater, deleted FROM system_role_menu WHERE deleted = 0 AND (menu_id = ?) AND tenant_id = 1
2025-08-01 10:46:47.310 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | ==> Parameters: 9228(Long)
2025-08-01 10:46:47.313 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | <==      Total: 0
2025-08-01 10:46:47.319 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | ==>  Preparing: SELECT id, role_id, menu_id, tenant_id, create_time, update_time, creator, updater, deleted FROM system_role_menu WHERE deleted = 0 AND (menu_id = ?) AND tenant_id = 1
2025-08-01 10:46:47.319 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | ==> Parameters: 9254(Long)
2025-08-01 10:46:47.321 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | <==      Total: 0
2025-08-01 10:46:47.330 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | ==>  Preparing: SELECT id, role_id, menu_id, tenant_id, create_time, update_time, creator, updater, deleted FROM system_role_menu WHERE deleted = 0 AND (menu_id = ?) AND tenant_id = 1
2025-08-01 10:46:47.331 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | ==> Parameters: 9255(Long)
2025-08-01 10:46:47.333 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.s.d.m.p.R.selectList             | <==      Total: 0
2025-08-01 10:46:47.353 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM mobile_devices WHERE tenant_id = 1
2025-08-01 10:46:47.353 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:46:47.355 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:46:47.356 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE tenant_id = 1 ORDER BY id DESC LIMIT ?
2025-08-01 10:46:47.357 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 10(Long)
2025-08-01 10:46:47.361 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:46:47.379 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:46:47.380 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:46:47.382 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:46:47.388 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/mobile/devices/page) 耗时(117 ms)]
2025-08-01 10:47:01.565 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.job.DeviceOfflineCheckJob      | 🔍 开始执行设备离线检测定时任务
2025-08-01 10:47:01.569 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:47:01.570 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:47:01.571 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:47:01.577 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE tenant_id = 1 ORDER BY id DESC
2025-08-01 10:47:01.578 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 
2025-08-01 10:47:01.579 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:47:01.580 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.job.DeviceOfflineCheckJob      | ✅ 设备离线检测完成，离线代理器: 0, 离线手机设备: 0
2025-08-01 10:47:03.236 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务开始]
2025-08-01 10:47:03.239 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE deleted = 0
2025-08-01 10:47:03.239 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==> Parameters: 
2025-08-01 10:47:03.240 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | <==      Total: 3
2025-08-01 10:47:03.253 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:47:03.262 | DEBUG 37956 | pool-3-thread-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:47:03.263 | DEBUG 37956 | pool-3-thread-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 1(Long)
2025-08-01 10:47:03.265 | DEBUG 37956 | pool-3-thread-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:47:03.269 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:47:03.271 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:47:03.272 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:47:03.273 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:47:03.278 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:47:03.279 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:47:03.280 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:47:03.282 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:47:03.283 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:47:03.280670(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:47:03.286 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:47:03.286 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:47:03.290 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:47:03.290 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:47:03.291 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:47:03.292 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:47:03
2025-08-01 10:47:03.292 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:47:03.293 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=true, Redis数据={machineId=3d95b6bb04a7e5bf8dddcd691d869b70, lastHeartbeatTime=2025-08-01T10:46:33.186989600}, 最后上报时间=2025-08-01T10:46:44, 最终状态=0(在线)
2025-08-01 10:47:03.295 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(26 ms)]
2025-08-01 10:47:03.296 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:47:03.296 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:47:03.305 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:47:03.309 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
2025-08-01 10:47:03.309 | DEBUG 37956 | pool-3-thread-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:47:03.310 | DEBUG 37956 | pool-3-thread-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 121(Long)
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:47:03.311 | DEBUG 37956 | pool-3-thread-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:47:03.313 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 121
2025-08-01 10:47:03.313 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:47:03.314 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 0
2025-08-01 10:47:03.316 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(7 ms)]
2025-08-01 10:47:03.317 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:47:03.318 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:47:03.328 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:47:03.331 |  WARN 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.f.w.c.h.GlobalExceptionHandler     | [serviceExceptionHandler]
	cn.iocoder.yudao.module.system.service.tenant.TenantServiceImpl.validTenant(TenantServiceImpl.java:94)
2025-08-01 10:47:03.332 | DEBUG 37956 | pool-3-thread-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:47:03.332 | DEBUG 37956 | pool-3-thread-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 122(Long)
2025-08-01 10:47:03.332 |  WARN 37956 | scheduling-1 [TID: N/A] c.i.y.m.channel.api.MonitorApiClient     | 获取设备列表失败: 名字为【测试租户】的租户已过期
2025-08-01 10:47:03.333 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:47:03.333 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:47:03.333 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务结束]
2025-08-01 10:47:03.333 | DEBUG 37956 | pool-3-thread-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:47:05.861 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/broagent/api/devices) 参数({"devices": [{"device": "9DRKU4X4VS69YXFI", "machine": "3d95b6bb04a7e5bf8dddcd691d869b70", "douyinStatus": 2, "xiaohongshuStatus": 2, "wechatStatus": 0, "qywxStatus": 2}], "timestamp": "2025-08-01T10:47:05.856797", "client_version": "1.0.0"})]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceReportController(DeviceReportController.java:34)
2025-08-01 10:47:05.863 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.c.DeviceReportController       | 接收设备上报请求: DeviceReportReqDTO(devices=[DeviceReportReqDTO.DeviceInfo(device=9DRKU4X4VS69YXFI, machine=3d95b6bb04a7e5bf8dddcd691d869b70, douyinStatus=2, xiaohongshuStatus=2, videoStatus=null, officialAccountStatus=null, qywxStatus=2, wechatStatus=0, dingtalkStatus=null, feishuStatus=null)])
2025-08-01 10:47:05.863 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 接收到设备上报数据: DeviceReportReqDTO(devices=[DeviceReportReqDTO.DeviceInfo(device=9DRKU4X4VS69YXFI, machine=3d95b6bb04a7e5bf8dddcd691d869b70, douyinStatus=2, xiaohongshuStatus=2, videoStatus=null, officialAccountStatus=null, qywxStatus=2, wechatStatus=0, dingtalkStatus=null, feishuStatus=null)])
2025-08-01 10:47:05.869 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:47:05.869 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:47:05.871 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:47:05.873 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.mysql.MachinesMapper.update  | ==>  Preparing: UPDATE machines SET machine_id = ?, last_report_time = ?, update_time = ?, updater = ? WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:47:05.873 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.mysql.MachinesMapper.update  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String), 2025-08-01T10:47:05.871287600(LocalDateTime), 2025-08-01T10:47:05.871287600(LocalDateTime), null, 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:47:05.875 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.mysql.MachinesMapper.update  | <==    Updates: 1
2025-08-01 10:47:05.877 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:47:05.878 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:47:05.876273700(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:47:05.880 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:47:05.880 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:47:05.882 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:47:05.882 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:47:05.884 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:47:05.885 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:47:06
2025-08-01 10:47:05.885 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI
2025-08-01 10:47:05.887 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:47:05.887 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:47:05.889 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:47:05.891 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:47:05.892 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:47:05.894 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:47:05.896 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:47:05.897 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:47:05.899 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:47:05.901 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:47:05.902 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:47:05.904 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:47:05.906 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/broagent/api/devices) 耗时(44 ms)]
2025-08-01 10:47:09.085 | ERROR 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.c.w.AgentWebSocketEndpoint       | [onError][WebSocket发生错误] sessionId: 0, 错误信息: Connection reset

java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at org.apache.tomcat.util.net.NioChannel.read(NioChannel.java:147)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1293)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.read(NioEndpoint.java:1183)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:74)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:833)

2025-08-01 10:47:09.089 | ERROR 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.c.w.AgentWebSocketEndpoint       | [onError][WebSocket发生错误] sessionId: 0, 错误信息: java.io.IOException: Connection reset by peer

java.io.IOException: java.io.IOException: Connection reset by peer
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase.sendMessageBlockInternal(WsRemoteEndpointImplBase.java:326)
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase.sendMessageBlock(WsRemoteEndpointImplBase.java:266)
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase.sendMessageBlock(WsRemoteEndpointImplBase.java:257)
	at org.apache.tomcat.websocket.WsSession.sendCloseMessage(WsSession.java:776)
	at org.apache.tomcat.websocket.WsSession.doClose(WsSession.java:575)
	at org.apache.tomcat.websocket.WsSession.doClose(WsSession.java:542)
	at org.apache.tomcat.websocket.WsSession.close(WsSession.java:530)
	at org.apache.tomcat.websocket.WsSession.close(WsSession.java:524)
	at cn.iocoder.yudao.module.channel.websocket.AgentWebSocketEndpoint.onError(AgentWebSocketEndpoint.java:112)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.tomcat.websocket.pojo.PojoEndpointBase.onError(PojoEndpointBase.java:126)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.onError(WsHttpUpgradeHandler.java:234)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:156)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: java.io.IOException: Connection reset by peer
	at java.base/sun.nio.ch.SocketDispatcher.writev0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.writev(SocketDispatcher.java:58)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:217)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:153)
	at java.base/sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:563)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:134)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper$NioOperationState.run(NioEndpoint.java:1634)
	at org.apache.tomcat.util.net.SocketWrapperBase$OperationState.start(SocketWrapperBase.java:1037)
	at org.apache.tomcat.util.net.SocketWrapperBase.vectoredOperation(SocketWrapperBase.java:1426)
	at org.apache.tomcat.util.net.SocketWrapperBase.write(SocketWrapperBase.java:1352)
	at org.apache.tomcat.util.net.SocketWrapperBase.write(SocketWrapperBase.java:1323)
	at org.apache.tomcat.websocket.server.WsRemoteEndpointImplServer.doWrite(WsRemoteEndpointImplServer.java:171)
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase.writeMessagePart(WsRemoteEndpointImplBase.java:521)
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase.sendMessageBlockInternal(WsRemoteEndpointImplBase.java:313)
	... 24 common frames omitted

2025-08-01 10:47:09.090 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.c.w.AgentWebSocketEndpoint       | [onClose][清理机器ID映射] machineId: 3d95b6bb04a7e5bf8dddcd691d869b70
2025-08-01 10:47:09.090 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.c.w.AgentWebSocketEndpoint       | [onClose][WebSocket连接关闭] sessionId: 0, 当前在线数: 0
2025-08-01 10:47:33.335 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务开始]
2025-08-01 10:47:33.337 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE deleted = 0
2025-08-01 10:47:33.338 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==> Parameters: 
2025-08-01 10:47:33.339 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | <==      Total: 3
2025-08-01 10:47:33.348 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:47:33.350 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:47:33.354 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:47:33.354 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:47:33.356 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:47:33.360 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:47:33.360 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:47:33.361 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:47:33.363 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:47:33.363 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:47:33.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:47:33.723 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:47:33.723 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:47:33.726 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:47:33.726 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:47:33.728 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:47:33.728 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:47:33
2025-08-01 10:47:33.728 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:47:33.729 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=true, Redis数据={machineId=3d95b6bb04a7e5bf8dddcd691d869b70, lastHeartbeatTime=2025-08-01T10:47:05.864776}, 最后上报时间=2025-08-01T10:47:06, 最终状态=0(在线)
2025-08-01 10:47:33.730 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(378 ms)]
2025-08-01 10:47:33.731 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:47:33.731 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:47:33.739 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:47:33.742 |  INFO 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:47:33.745 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 121
2025-08-01 10:47:33.746 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:47:33.747 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 0
2025-08-01 10:47:33.748 |  INFO 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(5 ms)]
2025-08-01 10:47:33.748 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:47:33.748 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:47:33.756 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:47:33.758 |  WARN 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.f.w.c.h.GlobalExceptionHandler     | [serviceExceptionHandler]
	cn.iocoder.yudao.module.system.service.tenant.TenantServiceImpl.validTenant(TenantServiceImpl.java:94)
2025-08-01 10:47:33.759 |  WARN 37956 | scheduling-1 [TID: N/A] c.i.y.m.channel.api.MonitorApiClient     | 获取设备列表失败: 名字为【测试租户】的租户已过期
2025-08-01 10:47:33.759 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:47:33.759 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:47:33.760 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务结束]
2025-08-01 10:47:50.087 |  INFO 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/system/notify-message/get-unread-count) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.system.controller.admin.notify.NotifyMessageController(NotifyMessageController.java:93)
2025-08-01 10:47:50.091 | DEBUG 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.s.d.m.n.N.selectCount            | ==>  Preparing: SELECT COUNT(*) AS total FROM system_notify_message WHERE deleted = 0 AND (read_status = ? AND user_id = ? AND user_type = ?) AND tenant_id = 1
2025-08-01 10:47:50.091 | DEBUG 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.s.d.m.n.N.selectCount            | ==> Parameters: false(Boolean), 1(Long), 2(Integer)
2025-08-01 10:47:50.092 | DEBUG 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.s.d.m.n.N.selectCount            | <==      Total: 1
2025-08-01 10:47:50.093 |  INFO 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/system/notify-message/get-unread-count) 耗时(6 ms)]
2025-08-01 10:48:01.565 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.job.DeviceOfflineCheckJob      | 🔍 开始执行设备离线检测定时任务
2025-08-01 10:48:01.567 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:48:01.567 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:48:01.568 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:48:01.572 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE tenant_id = 1 ORDER BY id DESC
2025-08-01 10:48:01.572 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 
2025-08-01 10:48:01.573 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:48:01.574 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.job.DeviceOfflineCheckJob      | ✅ 设备离线检测完成，离线代理器: 0, 离线手机设备: 0
2025-08-01 10:48:03.760 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务开始]
2025-08-01 10:48:03.762 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE deleted = 0
2025-08-01 10:48:03.762 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==> Parameters: 
2025-08-01 10:48:03.764 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | <==      Total: 3
2025-08-01 10:48:03.773 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:48:03.777 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:48:03.779 | DEBUG 37956 | pool-3-thread-2 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:48:03.780 | DEBUG 37956 | pool-3-thread-2 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 1(Long)
2025-08-01 10:48:03.781 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:48:03.781 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:48:03.781 | DEBUG 37956 | pool-3-thread-2 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:48:03.782 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:48:03.786 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:48:03.786 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:48:03.787 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:48:03.789 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:48:03.789 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:48:03.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:48:03.792 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:48:03.792 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:48:03.795 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:48:03.795 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:48:03.796 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:48:03.797 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:48:04
2025-08-01 10:48:03.797 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:48:03.797 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=true, Redis数据={machineId=3d95b6bb04a7e5bf8dddcd691d869b70, lastHeartbeatTime=2025-08-01T10:47:05.864776}, 最后上报时间=2025-08-01T10:47:33, 最终状态=0(在线)
2025-08-01 10:48:03.799 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(21 ms)]
2025-08-01 10:48:03.800 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:48:03.800 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:48:03.807 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:48:03.810 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
2025-08-01 10:48:03.811 | DEBUG 37956 | pool-3-thread-2 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:48:03.812 | DEBUG 37956 | pool-3-thread-2 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 121(Long)
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:48:03.813 | DEBUG 37956 | pool-3-thread-2 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:48:03.814 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 121
2025-08-01 10:48:03.814 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:48:03.816 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 0
2025-08-01 10:48:03.817 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(6 ms)]
2025-08-01 10:48:03.818 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:48:03.818 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:48:03.826 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:48:03.829 |  WARN 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.f.w.c.h.GlobalExceptionHandler     | [serviceExceptionHandler]
	cn.iocoder.yudao.module.system.service.tenant.TenantServiceImpl.validTenant(TenantServiceImpl.java:94)
2025-08-01 10:48:03.831 | DEBUG 37956 | pool-3-thread-2 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:48:03.831 |  WARN 37956 | scheduling-1 [TID: N/A] c.i.y.m.channel.api.MonitorApiClient     | 获取设备列表失败: 名字为【测试租户】的租户已过期
2025-08-01 10:48:03.831 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:48:03.831 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:48:03.831 | DEBUG 37956 | pool-3-thread-2 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 122(Long)
2025-08-01 10:48:03.831 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务结束]
2025-08-01 10:48:03.832 | DEBUG 37956 | pool-3-thread-2 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:48:33.832 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务开始]
2025-08-01 10:48:33.834 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE deleted = 0
2025-08-01 10:48:33.834 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==> Parameters: 
2025-08-01 10:48:33.836 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | <==      Total: 3
2025-08-01 10:48:33.846 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:48:33.850 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:48:33.854 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:48:33.854 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:48:33.856 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:48:33.860 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:48:33.860 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:48:33.861 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:48:33.863 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:48:33.864 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:48:33.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:48:33.866 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:48:33.866 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:48:33.868 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:48:33.869 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:48:33.870 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:48:33.870 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:48:34
2025-08-01 10:48:33.871 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:48:33.871 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=true, Redis数据={machineId=3d95b6bb04a7e5bf8dddcd691d869b70, lastHeartbeatTime=2025-08-01T10:47:05.864776}, 最后上报时间=2025-08-01T10:48:04, 最终状态=0(在线)
2025-08-01 10:48:33.873 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(23 ms)]
2025-08-01 10:48:33.874 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:48:33.874 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:48:33.881 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:48:33.884 |  INFO 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:48:33.888 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 121
2025-08-01 10:48:33.888 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:48:33.889 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 0
2025-08-01 10:48:33.891 |  INFO 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(6 ms)]
2025-08-01 10:48:33.892 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:48:33.892 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:48:33.900 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:48:33.903 |  WARN 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.f.w.c.h.GlobalExceptionHandler     | [serviceExceptionHandler]
	cn.iocoder.yudao.module.system.service.tenant.TenantServiceImpl.validTenant(TenantServiceImpl.java:94)
2025-08-01 10:48:33.905 |  WARN 37956 | scheduling-1 [TID: N/A] c.i.y.m.channel.api.MonitorApiClient     | 获取设备列表失败: 名字为【测试租户】的租户已过期
2025-08-01 10:48:33.905 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:48:33.905 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:48:33.905 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务结束]
2025-08-01 10:49:01.564 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.job.DeviceOfflineCheckJob      | 🔍 开始执行设备离线检测定时任务
2025-08-01 10:49:01.566 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:49:01.566 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:49:01.567 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:49:01.570 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE tenant_id = 1 ORDER BY id DESC
2025-08-01 10:49:01.571 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 
2025-08-01 10:49:01.572 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:49:01.573 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.job.DeviceOfflineCheckJob      | ✅ 设备离线检测完成，离线代理器: 0, 离线手机设备: 0
2025-08-01 10:49:03.907 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务开始]
2025-08-01 10:49:03.909 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE deleted = 0
2025-08-01 10:49:03.909 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==> Parameters: 
2025-08-01 10:49:03.910 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | <==      Total: 3
2025-08-01 10:49:03.918 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:49:03.922 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:49:03.925 | DEBUG 37956 | pool-3-thread-3 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:49:03.925 | DEBUG 37956 | pool-3-thread-3 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 1(Long)
2025-08-01 10:49:03.926 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:49:03.926 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:49:03.926 | DEBUG 37956 | pool-3-thread-3 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:49:03.927 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:49:03.930 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:49:03.930 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:49:03.932 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:49:03.933 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:49:03.933 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:49:03.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:49:03.935 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:49:03.935 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:49:03.938 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:49:03.938 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:49:03.939 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:49:03.939 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:49:04
2025-08-01 10:49:03.940 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:49:03.940 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=true, Redis数据={machineId=3d95b6bb04a7e5bf8dddcd691d869b70, lastHeartbeatTime=2025-08-01T10:47:05.864776}, 最后上报时间=2025-08-01T10:48:34, 最终状态=0(在线)
2025-08-01 10:49:03.942 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(19 ms)]
2025-08-01 10:49:03.942 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:49:03.942 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:49:03.949 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:49:03.952 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
2025-08-01 10:49:03.953 | DEBUG 37956 | pool-3-thread-3 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:49:03.953 | DEBUG 37956 | pool-3-thread-3 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 121(Long)
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:49:03.954 | DEBUG 37956 | pool-3-thread-3 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:49:03.956 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 121
2025-08-01 10:49:03.956 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:49:03.957 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 0
2025-08-01 10:49:03.958 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(6 ms)]
2025-08-01 10:49:03.959 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:49:03.959 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:49:03.966 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:49:03.968 |  WARN 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.f.w.c.h.GlobalExceptionHandler     | [serviceExceptionHandler]
	cn.iocoder.yudao.module.system.service.tenant.TenantServiceImpl.validTenant(TenantServiceImpl.java:94)
2025-08-01 10:49:03.969 | DEBUG 37956 | pool-3-thread-3 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:49:03.969 |  WARN 37956 | scheduling-1 [TID: N/A] c.i.y.m.channel.api.MonitorApiClient     | 获取设备列表失败: 名字为【测试租户】的租户已过期
2025-08-01 10:49:03.969 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:49:03.969 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:49:03.969 | DEBUG 37956 | pool-3-thread-3 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 122(Long)
2025-08-01 10:49:03.969 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务结束]
2025-08-01 10:49:03.971 | DEBUG 37956 | pool-3-thread-3 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:49:33.971 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务开始]
2025-08-01 10:49:33.974 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE deleted = 0
2025-08-01 10:49:33.974 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==> Parameters: 
2025-08-01 10:49:33.976 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | <==      Total: 3
2025-08-01 10:49:33.985 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:49:33.989 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:49:33.993 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:49:33.994 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:49:33.995 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:49:33.999 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:49:33.999 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:49:34.001 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:49:34.003 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:49:34.004 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:49:34.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:49:34.006 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:49:34.006 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:49:34.010 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:49:34.010 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:49:34.011 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:49:34.012 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:49:34
2025-08-01 10:49:34.012 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:49:34.013 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=false, Redis数据={}, 最后上报时间=2025-08-01T10:49:04, 最终状态=0(在线)
2025-08-01 10:49:34.014 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(24 ms)]
2025-08-01 10:49:34.015 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:49:34.016 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:49:34.023 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:49:34.026 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:49:34.029 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 121
2025-08-01 10:49:34.029 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:49:34.030 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 0
2025-08-01 10:49:34.031 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(5 ms)]
2025-08-01 10:49:34.032 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:49:34.033 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:49:34.041 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:49:34.044 |  WARN 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.f.w.c.h.GlobalExceptionHandler     | [serviceExceptionHandler]
	cn.iocoder.yudao.module.system.service.tenant.TenantServiceImpl.validTenant(TenantServiceImpl.java:94)
2025-08-01 10:49:34.045 |  WARN 37956 | scheduling-1 [TID: N/A] c.i.y.m.channel.api.MonitorApiClient     | 获取设备列表失败: 名字为【测试租户】的租户已过期
2025-08-01 10:49:34.045 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:49:34.045 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:49:34.046 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务结束]
2025-08-01 10:49:35.993 |  INFO 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:49:35.996 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:49:35.996 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:49:35.998 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:49:36.000 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:49:36.000 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:49:36.002 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:49:36.003 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:49:36.004 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:49:36.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:49:36.006 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:49:36.006 |  INFO 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:49:36.007 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:49:36.008 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:49:36.009 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:49:36.009 |  INFO 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:49:36
2025-08-01 10:49:36.009 |  INFO 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:49:36.010 |  INFO 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=false, Redis数据={}, 最后上报时间=2025-08-01T10:49:34, 最终状态=0(在线)
2025-08-01 10:49:36.011 |  INFO 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(17 ms)]
2025-08-01 10:49:37.270 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:49:37.272 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:49:37.272 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:49:37.274 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:49:37.276 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:49:37.277 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:49:37.279 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:49:37.281 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:49:37.281 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:49:37.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:49:37.283 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:49:37.284 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:49:37.285 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:49:37.285 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:49:37.286 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:49:37.287 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:49:37
2025-08-01 10:49:37.287 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:49:37.288 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=false, Redis数据={}, 最后上报时间=2025-08-01T10:49:36, 最终状态=0(在线)
2025-08-01 10:49:37.289 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(19 ms)]
2025-08-01 10:49:39.365 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:86)
2025-08-01 10:49:39.381 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM machines WHERE tenant_id = 1
2025-08-01 10:49:39.381 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:49:39.383 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:49:39.384 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1 ORDER BY create_time DESC, id DESC LIMIT ?
2025-08-01 10:49:39.384 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 10(Long)
2025-08-01 10:49:39.386 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:49:39.388 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/page) 耗时(23 ms)]
2025-08-01 10:49:40.638 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:86)
2025-08-01 10:49:40.648 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM machines WHERE tenant_id = 1
2025-08-01 10:49:40.648 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:49:40.649 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:49:40.650 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1 ORDER BY create_time DESC, id DESC LIMIT ?
2025-08-01 10:49:40.650 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 10(Long)
2025-08-01 10:49:40.651 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:49:40.652 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/page) 耗时(14 ms)]
2025-08-01 10:49:41.703 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/list) 无参数]
2025-08-01 10:49:41.703 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/mobile/devices/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.mobile.controller.admin.MobileDeviceController(MobileDeviceController.java:77)
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:94)
2025-08-01 10:49:41.715 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:49:41.715 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:49:41.716 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM mobile_devices WHERE tenant_id = 1
2025-08-01 10:49:41.716 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:49:41.716 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:49:41.716 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:49:41.717 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE tenant_id = 1 ORDER BY id DESC LIMIT ?
2025-08-01 10:49:41.717 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 10(Long)
2025-08-01 10:49:41.718 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:49:41.718 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/list) 耗时(14 ms)]
2025-08-01 10:49:41.721 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:49:41.721 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:49:41.722 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:49:41.724 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/mobile/devices/page) 耗时(20 ms)]
2025-08-01 10:49:43.604 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/mobile/devices/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.mobile.controller.admin.MobileDeviceController(MobileDeviceController.java:77)
2025-08-01 10:49:43.615 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM mobile_devices WHERE tenant_id = 1
2025-08-01 10:49:43.615 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:49:43.616 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:49:43.617 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE tenant_id = 1 ORDER BY id DESC LIMIT ?
2025-08-01 10:49:43.617 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 10(Long)
2025-08-01 10:49:43.618 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:49:43.620 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:49:43.620 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:49:43.621 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:49:43.622 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/mobile/devices/page) 耗时(18 ms)]
2025-08-01 10:49:43.847 |  INFO 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/mobile/devices/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.mobile.controller.admin.MobileDeviceController(MobileDeviceController.java:77)
2025-08-01 10:49:43.862 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM mobile_devices WHERE tenant_id = 1
2025-08-01 10:49:43.862 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:49:43.863 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:49:43.864 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE tenant_id = 1 ORDER BY id DESC LIMIT ?
2025-08-01 10:49:43.864 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 10(Long)
2025-08-01 10:49:43.866 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:49:43.867 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:49:43.868 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:49:43.870 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:49:43.871 |  INFO 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/mobile/devices/page) 耗时(24 ms)]
2025-08-01 10:49:49.136 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/system/notify-message/get-unread-count) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.system.controller.admin.notify.NotifyMessageController(NotifyMessageController.java:93)
2025-08-01 10:49:49.139 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.s.d.m.n.N.selectCount            | ==>  Preparing: SELECT COUNT(*) AS total FROM system_notify_message WHERE deleted = 0 AND (read_status = ? AND user_id = ? AND user_type = ?) AND tenant_id = 1
2025-08-01 10:49:49.140 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.s.d.m.n.N.selectCount            | ==> Parameters: false(Boolean), 1(Long), 2(Integer)
2025-08-01 10:49:49.141 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.s.d.m.n.N.selectCount            | <==      Total: 1
2025-08-01 10:49:49.142 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/system/notify-message/get-unread-count) 耗时(5 ms)]
2025-08-01 10:50:01.564 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.job.DeviceOfflineCheckJob      | 🔍 开始执行设备离线检测定时任务
2025-08-01 10:50:01.566 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:50:01.566 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:50:01.568 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:50:01.571 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.updateById  | ==>  Preparing: UPDATE machines SET status = ?, update_time = ?, updater = ? WHERE id = ? AND tenant_id = 1
2025-08-01 10:50:01.571 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.updateById  | ==> Parameters: 1(Integer), 2025-08-01T10:50:01.569863300(LocalDateTime), null, 20(Long)
2025-08-01 10:50:01.574 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.updateById  | <==    Updates: 1
2025-08-01 10:50:01.574 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.service.MachinesServiceImpl    | 🔥 离线检测：更新代理器状态 id=20, status=1
2025-08-01 10:50:01.575 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.job.DeviceOfflineCheckJob      | 🔴 代理器离线状态同步: machineId=3d95b6bb04a7e5bf8dddcd691d869b70, name=null
2025-08-01 10:50:01.577 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE tenant_id = 1 ORDER BY id DESC
2025-08-01 10:50:01.578 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 
2025-08-01 10:50:01.579 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:50:01.581 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.job.DeviceOfflineCheckJob      | ✅ 设备离线检测完成，离线代理器: 1, 离线手机设备: 0
2025-08-01 10:50:04.047 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务开始]
2025-08-01 10:50:04.049 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE deleted = 0
2025-08-01 10:50:04.049 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==> Parameters: 
2025-08-01 10:50:04.050 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | <==      Total: 3
2025-08-01 10:50:04.058 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:50:04.062 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:50:04.064 | DEBUG 37956 | pool-3-thread-4 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:50:04.064 | DEBUG 37956 | pool-3-thread-4 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 1(Long)
2025-08-01 10:50:04.065 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:50:04.065 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:50:04.065 | DEBUG 37956 | pool-3-thread-4 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:50:04.066 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:50:04.069 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:50:04.069 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:50:04.070 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:50:04.072 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:50:04.072 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:50:04.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:50:04.074 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:50:04.074 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:50:04.077 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:50:04.077 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:50:04.078 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:50:04.078 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:50:04
2025-08-01 10:50:04.078 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:50:04.079 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=false, Redis数据={}, 最后上报时间=2025-08-01T10:49:37, 最终状态=0(在线)
2025-08-01 10:50:04.083 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(20 ms)]
2025-08-01 10:50:04.084 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:50:04.084 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:50:04.091 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:50:04.094 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
2025-08-01 10:50:04.095 | DEBUG 37956 | pool-3-thread-4 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:50:04.095 | DEBUG 37956 | pool-3-thread-4 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 121(Long)
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:50:04.096 | DEBUG 37956 | pool-3-thread-4 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:50:04.097 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 121
2025-08-01 10:50:04.098 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:50:04.098 | DEBUG 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 0
2025-08-01 10:50:04.100 |  INFO 37956 | http-nio-48080-exec-5 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(5 ms)]
2025-08-01 10:50:04.100 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:50:04.100 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:50:04.107 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:50:04.110 |  WARN 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.f.w.c.h.GlobalExceptionHandler     | [serviceExceptionHandler]
	cn.iocoder.yudao.module.system.service.tenant.TenantServiceImpl.validTenant(TenantServiceImpl.java:94)
2025-08-01 10:50:04.111 | DEBUG 37956 | pool-3-thread-4 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:50:04.112 | DEBUG 37956 | pool-3-thread-4 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 122(Long)
2025-08-01 10:50:04.112 |  WARN 37956 | scheduling-1 [TID: N/A] c.i.y.m.channel.api.MonitorApiClient     | 获取设备列表失败: 名字为【测试租户】的租户已过期
2025-08-01 10:50:04.112 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:50:04.112 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:50:04.112 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务结束]
2025-08-01 10:50:04.113 | DEBUG 37956 | pool-3-thread-4 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:50:34.114 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务开始]
2025-08-01 10:50:34.116 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE deleted = 0
2025-08-01 10:50:34.116 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==> Parameters: 
2025-08-01 10:50:34.117 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | <==      Total: 3
2025-08-01 10:50:34.125 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:50:34.128 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:50:34.132 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:50:34.132 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:50:34.133 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:50:34.136 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:50:34.136 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:50:34.137 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:50:34.139 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:50:34.139 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:50:34.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:50:34.141 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:50:34.142 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:50:34.144 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:50:34.144 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:50:34.145 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:50:34.145 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:50:34
2025-08-01 10:50:34.145 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:50:34.146 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=false, Redis数据={}, 最后上报时间=2025-08-01T10:50:04, 最终状态=0(在线)
2025-08-01 10:50:34.147 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(19 ms)]
2025-08-01 10:50:34.148 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:50:34.148 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:50:34.155 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:50:34.158 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:50:34.161 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 121
2025-08-01 10:50:34.161 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:50:34.162 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 0
2025-08-01 10:50:34.163 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(5 ms)]
2025-08-01 10:50:34.164 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:50:34.164 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:50:34.171 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:50:34.173 |  WARN 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.f.w.c.h.GlobalExceptionHandler     | [serviceExceptionHandler]
	cn.iocoder.yudao.module.system.service.tenant.TenantServiceImpl.validTenant(TenantServiceImpl.java:94)
2025-08-01 10:50:34.174 |  WARN 37956 | scheduling-1 [TID: N/A] c.i.y.m.channel.api.MonitorApiClient     | 获取设备列表失败: 名字为【测试租户】的租户已过期
2025-08-01 10:50:34.174 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:50:34.175 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:50:34.175 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务结束]
2025-08-01 10:50:51.592 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:86)
2025-08-01 10:50:51.605 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM machines WHERE tenant_id = 1
2025-08-01 10:50:51.605 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:50:51.606 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:50:51.607 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1 ORDER BY create_time DESC, id DESC LIMIT ?
2025-08-01 10:50:51.607 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 10(Long)
2025-08-01 10:50:51.608 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:50:51.610 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/page) 耗时(17 ms)]
2025-08-01 10:50:53.278 |  INFO 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/list) 无参数]
2025-08-01 10:50:53.278 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/mobile/devices/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:94)
	Controller 方法路径：cn.iocoder.yudao.module.mobile.controller.admin.MobileDeviceController(MobileDeviceController.java:77)
2025-08-01 10:50:53.287 | DEBUG 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:50:53.287 | DEBUG 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:50:53.288 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM mobile_devices WHERE tenant_id = 1
2025-08-01 10:50:53.288 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:50:53.288 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:50:53.289 | DEBUG 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:50:53.289 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE tenant_id = 1 ORDER BY id DESC LIMIT ?
2025-08-01 10:50:53.289 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 10(Long)
2025-08-01 10:50:53.290 |  INFO 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/list) 耗时(12 ms)]
2025-08-01 10:50:53.290 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:50:53.294 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:50:53.294 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:50:53.295 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:50:53.296 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/mobile/devices/page) 耗时(18 ms)]
2025-08-01 10:50:54.827 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/mobile/devices/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.mobile.controller.admin.MobileDeviceController(MobileDeviceController.java:77)
2025-08-01 10:50:54.838 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM mobile_devices WHERE tenant_id = 1
2025-08-01 10:50:54.838 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:50:54.839 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:50:54.840 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE tenant_id = 1 ORDER BY id DESC LIMIT ?
2025-08-01 10:50:54.840 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 10(Long)
2025-08-01 10:50:54.842 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:50:54.844 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:50:54.844 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:50:54.846 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:50:54.847 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/mobile/devices/page) 耗时(19 ms)]
2025-08-01 10:50:55.937 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:86)
2025-08-01 10:50:55.949 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM machines WHERE tenant_id = 1
2025-08-01 10:50:55.950 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:50:55.951 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:50:55.951 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1 ORDER BY create_time DESC, id DESC LIMIT ?
2025-08-01 10:50:55.952 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 10(Long)
2025-08-01 10:50:55.953 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:50:55.955 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/page) 耗时(17 ms)]
2025-08-01 10:50:58.304 |  INFO 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:86)
2025-08-01 10:50:58.315 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM machines WHERE tenant_id = 1
2025-08-01 10:50:58.315 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:50:58.316 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:50:58.316 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1 ORDER BY create_time DESC, id DESC LIMIT ?
2025-08-01 10:50:58.316 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 10(Long)
2025-08-01 10:50:58.317 | DEBUG 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:50:58.319 |  INFO 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/page) 耗时(15 ms)]
2025-08-01 10:51:00.762 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:51:00.767 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:51:00.767 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:51:00.768 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:51:00.772 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:51:00.772 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:51:00.774 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:51:00.776 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:51:00.776 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:51:00.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:51:00.779 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:51:00.779 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:51:00.782 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:51:00.782 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:51:00.783 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:51:00.784 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:51:01
2025-08-01 10:51:00.784 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:51:00.785 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=false, Redis数据={}, 最后上报时间=2025-08-01T10:50:34, 最终状态=0(在线)
2025-08-01 10:51:00.786 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(23 ms)]
2025-08-01 10:51:01.566 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.job.DeviceOfflineCheckJob      | 🔍 开始执行设备离线检测定时任务
2025-08-01 10:51:01.567 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:51:01.567 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:51:01.568 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:51:01.571 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE tenant_id = 1 ORDER BY id DESC
2025-08-01 10:51:01.571 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 
2025-08-01 10:51:01.572 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:51:01.573 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.job.DeviceOfflineCheckJob      | ✅ 设备离线检测完成，离线代理器: 0, 离线手机设备: 0
2025-08-01 10:51:02.932 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:86)
2025-08-01 10:51:02.943 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM machines WHERE tenant_id = 1
2025-08-01 10:51:02.943 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:51:02.944 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:51:02.945 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1 ORDER BY create_time DESC, id DESC LIMIT ?
2025-08-01 10:51:02.945 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 10(Long)
2025-08-01 10:51:02.946 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:51:02.949 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/page) 耗时(16 ms)]
2025-08-01 10:51:03.649 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/machines/list) 无参数]
2025-08-01 10:51:03.649 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/mobile/devices/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.mobile.controller.admin.MobileDeviceController(MobileDeviceController.java:77)
	Controller 方法路径：cn.iocoder.yudao.module.machines.controller.admin.MachinesController(MachinesController.java:94)
2025-08-01 10:51:03.660 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:51:03.660 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:51:03.661 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:51:03.662 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM mobile_devices WHERE tenant_id = 1
2025-08-01 10:51:03.662 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:51:03.663 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:51:03.664 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE tenant_id = 1 ORDER BY id DESC LIMIT ?
2025-08-01 10:51:03.664 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/machines/list) 耗时(14 ms)]
2025-08-01 10:51:03.664 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 10(Long)
2025-08-01 10:51:03.666 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:51:03.669 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:51:03.669 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:51:03.670 | DEBUG 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:51:03.672 |  INFO 37956 | http-nio-48080-exec-6 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/mobile/devices/page) 耗时(22 ms)]
2025-08-01 10:51:04.175 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务开始]
2025-08-01 10:51:04.177 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE deleted = 0
2025-08-01 10:51:04.177 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==> Parameters: 
2025-08-01 10:51:04.178 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | <==      Total: 3
2025-08-01 10:51:04.186 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:51:04.191 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:51:04.193 | DEBUG 37956 | pool-3-thread-5 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:51:04.194 | DEBUG 37956 | pool-3-thread-5 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 1(Long)
2025-08-01 10:51:04.194 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:51:04.194 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:51:04.195 | DEBUG 37956 | pool-3-thread-5 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:51:04.195 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:51:04.197 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:51:04.198 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:51:04.199 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:51:04.200 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:51:04.201 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:51:04.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:51:04.202 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:51:04.203 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:51:04.204 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:51:04.204 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:51:04.205 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:51:04.206 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:51:04
2025-08-01 10:51:04.206 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:51:04.207 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=false, Redis数据={}, 最后上报时间=2025-08-01T10:51:01, 最终状态=0(在线)
2025-08-01 10:51:04.208 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(16 ms)]
2025-08-01 10:51:04.209 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:51:04.209 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:51:04.216 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:51:04.218 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
2025-08-01 10:51:04.219 | DEBUG 37956 | pool-3-thread-5 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:51:04.219 | DEBUG 37956 | pool-3-thread-5 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 121(Long)
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:51:04.220 | DEBUG 37956 | pool-3-thread-5 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:51:04.221 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 121
2025-08-01 10:51:04.222 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:51:04.222 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 0
2025-08-01 10:51:04.224 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(5 ms)]
2025-08-01 10:51:04.225 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:51:04.225 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:51:04.232 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:51:04.234 |  WARN 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.w.c.h.GlobalExceptionHandler     | [serviceExceptionHandler]
	cn.iocoder.yudao.module.system.service.tenant.TenantServiceImpl.validTenant(TenantServiceImpl.java:94)
2025-08-01 10:51:04.235 | DEBUG 37956 | pool-3-thread-5 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:51:04.235 | DEBUG 37956 | pool-3-thread-5 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 122(Long)
2025-08-01 10:51:04.235 |  WARN 37956 | scheduling-1 [TID: N/A] c.i.y.m.channel.api.MonitorApiClient     | 获取设备列表失败: 名字为【测试租户】的租户已过期
2025-08-01 10:51:04.235 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:51:04.235 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:51:04.236 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务结束]
2025-08-01 10:51:04.236 | DEBUG 37956 | pool-3-thread-5 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:51:05.060 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/mobile/devices/page) 参数({pageNo=1, pageSize=10})]
	Controller 方法路径：cn.iocoder.yudao.module.mobile.controller.admin.MobileDeviceController(MobileDeviceController.java:77)
2025-08-01 10:51:05.070 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==>  Preparing: SELECT COUNT(*) AS total FROM mobile_devices WHERE tenant_id = 1
2025-08-01 10:51:05.070 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | ==> Parameters: 
2025-08-01 10:51:05.071 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList_mpCount       | <==      Total: 1
2025-08-01 10:51:05.071 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE tenant_id = 1 ORDER BY id DESC LIMIT ?
2025-08-01 10:51:05.072 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 10(Long)
2025-08-01 10:51:05.072 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:51:05.074 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:51:05.075 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:51:05.076 | DEBUG 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:51:05.077 |  INFO 37956 | http-nio-48080-exec-7 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/mobile/devices/page) 耗时(16 ms)]
2025-08-01 10:51:34.237 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务开始]
2025-08-01 10:51:34.240 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE deleted = 0
2025-08-01 10:51:34.240 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==> Parameters: 
2025-08-01 10:51:34.242 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | <==      Total: 3
2025-08-01 10:51:34.252 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:51:34.257 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:51:34.261 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:51:34.262 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:51:34.263 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:51:34.267 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:51:34.267 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:51:34.269 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:51:34.270 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:51:34.271 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:51:34.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:51:34.273 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:51:34.274 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:51:34.276 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:51:34.277 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:51:34.278 | DEBUG 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:51:34.279 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:51:34
2025-08-01 10:51:34.279 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:51:34.280 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=false, Redis数据={}, 最后上报时间=2025-08-01T10:51:04, 最终状态=0(在线)
2025-08-01 10:51:34.281 |  INFO 37956 | http-nio-48080-exec-2 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(23 ms)]
2025-08-01 10:51:34.281 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:51:34.282 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:51:34.290 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:51:34.294 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:51:34.299 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 121
2025-08-01 10:51:34.299 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:51:34.301 | DEBUG 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 0
2025-08-01 10:51:34.303 |  INFO 37956 | http-nio-48080-exec-1 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(8 ms)]
2025-08-01 10:51:34.304 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:51:34.304 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:51:34.313 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:51:34.317 |  WARN 37956 | http-nio-48080-exec-4 [TID: N/A] c.i.y.f.w.c.h.GlobalExceptionHandler     | [serviceExceptionHandler]
	cn.iocoder.yudao.module.system.service.tenant.TenantServiceImpl.validTenant(TenantServiceImpl.java:94)
2025-08-01 10:51:34.319 |  WARN 37956 | scheduling-1 [TID: N/A] c.i.y.m.channel.api.MonitorApiClient     | 获取设备列表失败: 名字为【测试租户】的租户已过期
2025-08-01 10:51:34.319 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:51:34.319 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:51:34.319 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务结束]
2025-08-01 10:51:50.087 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/system/notify-message/get-unread-count) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.system.controller.admin.notify.NotifyMessageController(NotifyMessageController.java:93)
2025-08-01 10:51:50.090 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.s.d.m.n.N.selectCount            | ==>  Preparing: SELECT COUNT(*) AS total FROM system_notify_message WHERE deleted = 0 AND (read_status = ? AND user_id = ? AND user_type = ?) AND tenant_id = 1
2025-08-01 10:51:50.091 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.s.d.m.n.N.selectCount            | ==> Parameters: false(Boolean), 1(Long), 2(Integer)
2025-08-01 10:51:50.092 | DEBUG 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.m.s.d.m.n.N.selectCount            | <==      Total: 1
2025-08-01 10:51:50.093 |  INFO 37956 | http-nio-48080-exec-3 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/system/notify-message/get-unread-count) 耗时(5 ms)]
2025-08-01 10:52:01.566 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.job.DeviceOfflineCheckJob      | 🔍 开始执行设备离线检测定时任务
2025-08-01 10:52:01.567 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:52:01.568 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:52:01.569 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:52:01.571 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE tenant_id = 1 ORDER BY id DESC
2025-08-01 10:52:01.571 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 
2025-08-01 10:52:01.572 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:52:01.573 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.m.job.DeviceOfflineCheckJob      | ✅ 设备离线检测完成，离线代理器: 0, 离线手机设备: 0
2025-08-01 10:52:04.320 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务开始]
2025-08-01 10:52:04.322 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE deleted = 0
2025-08-01 10:52:04.322 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | ==> Parameters: 
2025-08-01 10:52:04.323 | DEBUG 37956 | scheduling-1 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectList  | <==      Total: 3
2025-08-01 10:52:04.330 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:52:04.334 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:52:04.335 | DEBUG 37956 | pool-3-thread-6 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:52:04.336 | DEBUG 37956 | pool-3-thread-6 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 1(Long)
2025-08-01 10:52:04.337 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 1
2025-08-01 10:52:04.337 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:52:04.337 | DEBUG 37956 | pool-3-thread-6 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:52:04.338 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 1
2025-08-01 10:52:04.341 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (machine_id = ?) AND tenant_id = 1
2025-08-01 10:52:04.342 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 3d95b6bb04a7e5bf8dddcd691d869b70(String)
2025-08-01 10:52:04.343 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:52:04.345 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==>  Preparing: UPDATE mobile_devices SET last_report_time = ? WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:52:04.345 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | ==> Parameters: 2025-08-01T10:52:04.*********(LocalDateTime), 9DRKU4X4VS69YXFI(String)
2025-08-01 10:52:04.347 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.MobileDeviceMapper.update  | <==    Updates: 1
2025-08-01 10:52:04.348 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 更新移动设备最后上报时间: deviceId=9DRKU4X4VS69YXFI, 更新行数=1
2025-08-01 10:52:04.350 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==>  Preparing: SELECT id, device_id, name, machine_id, os_model, sim_card1, sim_card2, status, douyin_status, xiaohongshu_status, video_status, official_account_status, qywx_status, wechat_status, dingtalk_status, feishu_status, last_report_time, remark, tenant_id, create_time, update_time, creator, updater FROM mobile_devices WHERE (device_id = ?) AND tenant_id = 1
2025-08-01 10:52:04.350 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | ==> Parameters: 9DRKU4X4VS69YXFI(String)
2025-08-01 10:52:04.351 | DEBUG 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.d.m.M.selectList               | <==      Total: 1
2025-08-01 10:52:04.351 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.s.MobileDeviceServiceImpl      | 🔍 验证更新结果: deviceId=9DRKU4X4VS69YXFI, 最后上报时间=2025-08-01T10:52:04
2025-08-01 10:52:04.351 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 已更新移动设备最后上报时间: 9DRKU4X4VS69YXFI
2025-08-01 10:52:04.352 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.m.m.s.impl.DeviceServiceImpl       | 🔍 设备状态判断: deviceId=9DRKU4X4VS69YXFI, 数据库状态=0, Redis在线=false, Redis数据={}, 最后上报时间=2025-08-01T10:51:34, 最终状态=0(在线)
2025-08-01 10:52:04.353 |  INFO 37956 | http-nio-48080-exec-9 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(19 ms)]
2025-08-01 10:52:04.354 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:52:04.354 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:52:04.360 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:52:04.363 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [preHandle][开始请求 URL(/admin-api/monitor/devices) 无参数]
2025-08-01 10:52:04.364 | DEBUG 37956 | pool-3-thread-6 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:52:04.364 | DEBUG 37956 | pool-3-thread-6 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 121(Long)
	Controller 方法路径：cn.iocoder.yudao.module.monitor.controller.DeviceController(DeviceController.java:37)
2025-08-01 10:52:04.365 | DEBUG 37956 | pool-3-thread-6 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
2025-08-01 10:52:04.366 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==>  Preparing: SELECT id, machine_id, name, status, os_type, run_days, register_time, last_report_time, remark, cloud_domain, tenant_id, create_time, update_time, creator, updater FROM machines WHERE tenant_id = 121
2025-08-01 10:52:04.366 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | ==> Parameters: 
2025-08-01 10:52:04.367 | DEBUG 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.m.m.d.m.MachinesMapper.selectList  | <==      Total: 0
2025-08-01 10:52:04.368 |  INFO 37956 | http-nio-48080-exec-8 [TID: N/A] c.i.y.f.a.c.i.ApiAccessLogInterceptor    | [afterCompletion][完成请求 URL(/admin-api/monitor/devices) 耗时(5 ms)]
2025-08-01 10:52:04.369 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:52:04.369 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:52:04.376 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线托管渠道]
2025-08-01 10:52:04.378 |  WARN 37956 | http-nio-48080-exec-10 [TID: N/A] c.i.y.f.w.c.h.GlobalExceptionHandler     | [serviceExceptionHandler]
	cn.iocoder.yudao.module.system.service.tenant.TenantServiceImpl.validTenant(TenantServiceImpl.java:94)
2025-08-01 10:52:04.379 | DEBUG 37956 | pool-3-thread-6 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==>  Preparing: SELECT id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, create_time, update_time, creator, updater, deleted FROM system_tenant WHERE id = ? AND deleted = 0
2025-08-01 10:52:04.379 | DEBUG 37956 | pool-3-thread-6 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | ==> Parameters: 122(Long)
2025-08-01 10:52:04.379 |  WARN 37956 | scheduling-1 [TID: N/A] c.i.y.m.channel.api.MonitorApiClient     | 获取设备列表失败: 名字为【测试租户】的租户已过期
2025-08-01 10:52:04.379 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][获取到0个在线设备]
2025-08-01 10:52:04.379 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][筛选出0个设备在线的托管渠道]
2025-08-01 10:52:04.379 |  INFO 37956 | scheduling-1 [TID: N/A] c.i.y.m.c.job.ChannelStatusCheckJob      | [executeReadUnread][读取未读消息定时任务结束]
2025-08-01 10:52:04.380 | DEBUG 37956 | pool-3-thread-6 [TID: N/A] c.i.y.m.s.d.m.t.TenantMapper.selectById  | <==      Total: 1
